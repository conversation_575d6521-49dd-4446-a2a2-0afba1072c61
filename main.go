package main

import (
	"bufio"
	"bytes"
	"compress/gzip"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha256"
	"crypto/sha512"
	"encoding/binary"
	"encoding/json"
	"flag"
	"fmt"
	"hash"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"
	"unicode/utf16"
	"unsafe"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
	"golang.org/x/sys/unix"
)

const Version = "Bella v1.0.0"

var (
	input          = flag.String("input", "", "Input file or device")
	output         = flag.String("output", "", "Output file or device")
	bs             = flag.Int("bs", 4096, "Block size in bytes")
	count          = flag.Int("count", -1, "Number of blocks to copy (-1 = all)")
	skip           = flag.Int64("skip", 0, "Blocks to skip at start of input")
	seek           = flag.Int64("seek", 0, "Blocks to seek at start of output")
	progress       = flag.Bool("progress", false, "Show progress")
	compress       = flag.String("compress", "", "Compression: gzip[1-9]|none (e.g., gzip6)")
	decompress     = flag.String("decompress", "", "Decompression: gzip|auto|none")
	verify         = flag.Bool("verify", false, "Verify output matches input")
	wipe           = flag.String("wipe", "", "Wipe mode: zero|random (output only)")
	checksum       = flag.String("checksum", "", "Hash algorithm: sha256|sha512|md5")
	examples       = flag.Bool("examples", false, "Show usage examples")
	version        = flag.Bool("version", false, "Show version and exit")
	sparse         = flag.Bool("sparse", false, "Enable sparse file support (skip zero blocks)")
	sparseOnly     = flag.Bool("sparse-only", false, "Aggressive sparse mode: only write data blocks, create holes for all zero blocks")
	throttle       = flag.Int64("throttle", 0, "Throttle bandwidth in bytes/second (0 = unlimited)")
	jsonOutput     = flag.String("json", "", "Output progress and results to JSON file (empty = disabled)")
	threads        = flag.Int("threads", 1, "Number of I/O threads for parallel operations")
	appendMode     = flag.Bool("append", false, "Append to output file instead of overwriting")
	dryRun         = flag.Bool("dry-run", false, "Show what would be done without executing")
	resume         = flag.String("resume", "", "Resume from checkpoint file (e.g., checkpoint.json)")
	skipEmpty      = flag.Bool("skipempty", false, "Copy only up to last partition end plus buffer, skipping empty space")
	tempDir        = flag.String("tempdir", "", "Directory for temporary files (default: system temp)")
	jsonMemory     = flag.Bool("json-memory", false, "Buffer JSON output in memory, write on completion")
	checkpointMem  = flag.Bool("checkpoint-memory", false, "Store checkpoints in memory, write only on cancel/close")
	skipBadSectors = flag.Bool("skip-bad-sectors", false, "Continue copying when encountering read errors, logging bad sectors and filling with zeros")
	partitionOnly  = flag.Bool("partition-only", false, "Copy only actual partition data regions, skipping unallocated space")
)

// Progress tracking structures
type ProgressInfo struct {
	BytesDone   int64   `json:"bytes_done"`
	BytesTotal  int64   `json:"bytes_total"`
	BlocksDone  int     `json:"blocks_done"`
	BlocksTotal int     `json:"blocks_total"`
	Speed       float64 `json:"speed_mbps"`
	AvgSpeed    float64 `json:"avg_speed_mbps"`
	ElapsedTime float64 `json:"elapsed_seconds"`
	ETA         float64 `json:"eta_seconds"`
	PercentDone float64 `json:"percent_done"`
	Operation   string  `json:"operation"`
}

// Bad sector tracking structures
type BadSector struct {
	BlockNumber int64  `json:"block_number"`
	Offset      int64  `json:"offset"`
	Size        int    `json:"size"`
	Error       string `json:"error"`
	Timestamp   int64  `json:"timestamp"`
}

type BadSectorTracker struct {
	BadSectors []BadSector `json:"bad_sectors"`
	TotalBad   int         `json:"total_bad_sectors"`
	mu         sync.Mutex
}

func NewBadSectorTracker() *BadSectorTracker {
	return &BadSectorTracker{
		BadSectors: make([]BadSector, 0),
		TotalBad:   0,
	}
}

func (bst *BadSectorTracker) AddBadSector(blockNum int64, offset int64, size int, err error) {
	bst.mu.Lock()
	defer bst.mu.Unlock()

	badSector := BadSector{
		BlockNumber: blockNum,
		Offset:      offset,
		Size:        size,
		Error:       err.Error(),
		Timestamp:   time.Now().Unix(),
	}

	bst.BadSectors = append(bst.BadSectors, badSector)
	bst.TotalBad++

	// Log the bad sector
	if isJSONMode() {
		// In JSON mode, we'll include bad sectors in the final result
		fmt.Fprintf(os.Stderr, "Bad sector detected at block %d (offset %d): %v\n", blockNum, offset, err)
	} else {
		fmt.Printf("Warning: Bad sector at block %d (offset %d): %v\n", blockNum, offset, err)
	}
}

func (bst *BadSectorTracker) GetBadSectors() []BadSector {
	bst.mu.Lock()
	defer bst.mu.Unlock()
	return append([]BadSector(nil), bst.BadSectors...)
}

func (bst *BadSectorTracker) GetTotalBadSectors() int {
	bst.mu.Lock()
	defer bst.mu.Unlock()
	return bst.TotalBad
}

// handleReadError handles read errors when skip-bad-sectors is enabled
// Returns a buffer filled with zeros and true if the error should be skipped
func handleReadError(err error, blockNum int64, offset int64, bs int, badSectorTracker *BadSectorTracker) ([]byte, bool) {
	if !*skipBadSectors {
		return nil, false
	}

	// Add to bad sector tracker
	badSectorTracker.AddBadSector(blockNum, offset, bs, err)

	// Create a zero-filled buffer to replace the bad sector
	zeroBuffer := make([]byte, bs)
	return zeroBuffer, true
}

// Checkpoint structure for resume functionality
type Checkpoint struct {
	Input      string `json:"input"`
	Output     string `json:"output"`
	BlockSize  int    `json:"block_size"`
	Count      int    `json:"count"`
	Skip       int64  `json:"skip"`
	Seek       int64  `json:"seek"`
	BytesDone  int64  `json:"bytes_done"`
	BlocksDone int    `json:"blocks_done"`
	Compress   string `json:"compress"`
	Decompress string `json:"decompress"`
	Sparse     bool   `json:"sparse"`
	Checksum   string `json:"checksum"`
	StartTime  int64  `json:"start_time"`
	LastUpdate int64  `json:"last_update"`
}

// CheckpointManager handles memory-based checkpointing with optional file writes
type CheckpointManager struct {
	checkpoint   *Checkpoint
	filename     string
	memoryMode   bool
	writeOnClose bool
	lastSaveTime time.Time
	saveInterval time.Duration
	tempDir      string
	userDir      string
}

// NewCheckpointManager creates a new checkpoint manager
func NewCheckpointManager(checkpoint *Checkpoint, filename string, memoryMode bool, tempDir string, userDir string) *CheckpointManager {
	return &CheckpointManager{
		checkpoint:   checkpoint,
		filename:     filename,
		memoryMode:   memoryMode,
		writeOnClose: true,
		saveInterval: 30 * time.Second, // Save to disk every 30 seconds in memory mode
		tempDir:      tempDir,
		userDir:      userDir,
	}
}

// Update updates the checkpoint in memory
func (cm *CheckpointManager) Update(bytesDone int64, blocksDone int) {
	cm.checkpoint.BytesDone = bytesDone
	cm.checkpoint.BlocksDone = blocksDone
	cm.checkpoint.LastUpdate = time.Now().Unix()

	// In memory mode, only save to disk periodically or if forced
	if !cm.memoryMode {
		// Direct mode - save immediately
		cm.SaveToDisk()
	} else if time.Since(cm.lastSaveTime) > cm.saveInterval {
		// Memory mode - save periodically
		cm.SaveToDisk()
		cm.lastSaveTime = time.Now()
	}
}

// SaveToDisk saves the checkpoint to disk
func (cm *CheckpointManager) SaveToDisk() error {
	// Determine the actual file path
	var filePath string
	if cm.userDir != "" {
		filePath = filepath.Join(cm.userDir, cm.filename)
	} else {
		filePath = filepath.Join(cm.tempDir, cm.filename)
	}

	return saveCheckpoint(filePath, cm.checkpoint)
}

// Close saves the checkpoint to disk if in memory mode and cleanup
func (cm *CheckpointManager) Close(success bool) error {
	if cm.memoryMode && cm.writeOnClose {
		if err := cm.SaveToDisk(); err != nil {
			return err
		}
	}

	// Clean up checkpoint file on successful completion
	if success {
		var filePath string
		if cm.userDir != "" {
			filePath = filepath.Join(cm.userDir, cm.filename)
		} else {
			filePath = filepath.Join(cm.tempDir, cm.filename)
		}
		os.Remove(filePath)
	}

	return nil
}

// getTempDirectory returns the appropriate temp directory
func getTempDirectory() string {
	if *tempDir != "" {
		// User specified directory
		if err := os.MkdirAll(*tempDir, 0755); err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Failed to create temp directory %s: %v\n", *tempDir, err)
			return os.TempDir()
		}
		return *tempDir
	}
	return os.TempDir()
}

// JSONManager handles memory-based JSON output with optional file writes
type JSONManager struct {
	filename     string
	memoryMode   bool
	buffer       []byte
	tempDir      string
	userDir      string
	writeOnClose bool
}

// NewJSONManager creates a new JSON manager
func NewJSONManager(filename string, memoryMode bool, tempDir string, userDir string) *JSONManager {
	return &JSONManager{
		filename:     filename,
		memoryMode:   memoryMode,
		buffer:       make([]byte, 0),
		tempDir:      tempDir,
		userDir:      userDir,
		writeOnClose: true,
	}
}

// Write writes JSON data
func (jm *JSONManager) Write(data []byte) error {
	if jm.memoryMode {
		// Store in memory buffer
		jm.buffer = append(jm.buffer, data...)
		return nil
	} else {
		// Write directly to file
		var filePath string
		if jm.userDir != "" {
			filePath = filepath.Join(jm.userDir, jm.filename)
		} else {
			filePath = filepath.Join(jm.tempDir, jm.filename)
		}

		file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
		if err != nil {
			return err
		}
		defer file.Close()

		_, err = file.Write(data)
		return err
	}
}

// Close writes buffered data to file if in memory mode
func (jm *JSONManager) Close() error {
	if jm.memoryMode && jm.writeOnClose && len(jm.buffer) > 0 {
		var filePath string
		if jm.userDir != "" {
			filePath = filepath.Join(jm.userDir, jm.filename)
		} else {
			filePath = filepath.Join(jm.tempDir, jm.filename)
		}

		return os.WriteFile(filePath, jm.buffer, 0644)
	}
	return nil
}

// Device information structure
type DeviceInfo struct {
	Path       string `json:"path"`
	Name       string `json:"name"`
	Size       int64  `json:"size"`
	BlockSize  int64  `json:"block_size"`
	Filesystem string `json:"filesystem"`
	Mountpoint string `json:"mountpoint"`
	Model      string `json:"model"`
	Serial     string `json:"serial"`
	Vendor     string `json:"vendor"`
	Removable  bool   `json:"removable"`
	ReadOnly   bool   `json:"readonly"`
	Rotational bool   `json:"rotational"`
}

// Operation result structure
type OperationResult struct {
	Success        bool              `json:"success"`
	Operation      string            `json:"operation"`
	Input          string            `json:"input,omitempty"`
	Output         string            `json:"output,omitempty"`
	BytesCopied    int64             `json:"bytes_copied,omitempty"`
	BlocksCopied   int               `json:"blocks_copied,omitempty"`
	ElapsedTime    float64           `json:"elapsed_seconds"`
	AvgSpeed       float64           `json:"avg_speed_mbps"`
	Checksums      map[string]string `json:"checksums,omitempty"`
	Verified       *bool             `json:"verified,omitempty"`
	Error          string            `json:"error,omitempty"`
	BadSectors     []BadSector       `json:"bad_sectors,omitempty"`
	BadSectorCount int               `json:"bad_sector_count,omitempty"`
}

// Throttle controller
type ThrottleController struct {
	bytesPerSecond  int64
	lastTime        time.Time
	bytesThisSecond int64
	mu              sync.Mutex
}

func main() {
	if err := runMain(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

// validateOptionConflicts checks for conflicting command-line options
func validateOptionConflicts() error {
	var conflicts []string

	// Check for mutually exclusive compression/decompression
	if *compress != "" && *compress != "none" && *decompress != "" && *decompress != "none" && *decompress != "auto" {
		conflicts = append(conflicts, "cannot specify both -compress and -decompress (except 'auto')")
	}

	// Check for sparse + compression conflicts
	if *sparse && *compress != "" && *compress != "none" {
		conflicts = append(conflicts, "-sparse is incompatible with -compress (compression disables sparse optimization)")
	}

	if *sparseOnly && *compress != "" && *compress != "none" {
		conflicts = append(conflicts, "-sparse-only is incompatible with -compress (compression disables sparse optimization)")
	}

	// Check for wipe + input conflicts
	if *wipe != "" && *input != "" {
		conflicts = append(conflicts, "-wipe cannot be used with -input (wipe is output-only operation)")
	}

	// Check for wipe + copy-specific options
	if *wipe != "" {
		if *verify {
			conflicts = append(conflicts, "-wipe cannot be used with -verify")
		}
		if *compress != "" && *compress != "none" {
			conflicts = append(conflicts, "-wipe cannot be used with -compress")
		}
		if *decompress != "" && *decompress != "none" {
			conflicts = append(conflicts, "-wipe cannot be used with -decompress")
		}
		if *sparse || *sparseOnly {
			conflicts = append(conflicts, "-wipe cannot be used with sparse options")
		}
		if *skip != 0 || *seek != 0 {
			conflicts = append(conflicts, "-wipe cannot be used with -skip or -seek")
		}
		if *appendMode {
			conflicts = append(conflicts, "-wipe cannot be used with -append")
		}
		if *skipEmpty {
			conflicts = append(conflicts, "-wipe cannot be used with -skipempty")
		}
	}

	// Check for resume + conflicting options
	if *resume != "" {
		if *input != "" || *output != "" {
			conflicts = append(conflicts, "-resume cannot be used with -input or -output (resume uses checkpoint file)")
		}
		if *wipe != "" {
			conflicts = append(conflicts, "-resume cannot be used with -wipe")
		}
	}

	// Check for append + seek conflicts
	if *appendMode && *seek != 0 {
		conflicts = append(conflicts, "-append cannot be used with -seek (append automatically seeks to end)")
	}

	// Check for verify + wipe conflicts (already covered above but being explicit)
	if *verify && *wipe != "" {
		conflicts = append(conflicts, "-verify cannot be used with -wipe (nothing to verify)")
	}

	// Check for skipempty + count conflicts
	if *skipEmpty && *count != -1 {
		conflicts = append(conflicts, "-skipempty cannot be used with -count (skipempty calculates count automatically)")
	}

	// Check for partition-only + count conflicts
	if *partitionOnly && *count != -1 {
		conflicts = append(conflicts, "-partition-only cannot be used with -count (partition-only calculates regions automatically)")
	}

	// Check for partition-only + skipempty conflicts
	if *partitionOnly && *skipEmpty {
		conflicts = append(conflicts, "-partition-only and -skipempty are mutually exclusive")
	}

	// Check for partition-only + skip/seek conflicts
	if *partitionOnly && (*skip != 0 || *seek != 0) {
		conflicts = append(conflicts, "-partition-only cannot be used with -skip or -seek (operates on partition regions)")
	}

	// Check for sparse-only + sparse conflicts
	if *sparse && *sparseOnly {
		conflicts = append(conflicts, "-sparse and -sparse-only are mutually exclusive")
	}

	// Check for JSON memory options without JSON output
	if *jsonMemory && *jsonOutput == "" {
		conflicts = append(conflicts, "-json-memory requires -json to be specified")
	}

	// Check for checkpoint memory without operations that use checkpoints
	if *checkpointMem && *input == "" && *resume == "" {
		conflicts = append(conflicts, "-checkpoint-memory requires copy operation (-input) or -resume")
	}

	// Check for dry-run + verify conflicts
	if *dryRun && *verify {
		conflicts = append(conflicts, "-dry-run cannot be used with -verify (no actual operation to verify)")
	}

	// Check for threads > 1 with incompatible options
	if *threads > 1 {
		if *sparse || *sparseOnly {
			conflicts = append(conflicts, "multi-threading (-threads > 1) is not compatible with sparse modes")
		}
	}

	// Report conflicts if any found
	if len(conflicts) > 0 {
		var errorMsg strings.Builder
		errorMsg.WriteString("Option conflicts detected:\n")
		for i, conflict := range conflicts {
			errorMsg.WriteString(fmt.Sprintf("  %d. %s\n", i+1, conflict))
		}
		errorMsg.WriteString("\nUse -h for help or run without arguments for interactive mode.")
		return fmt.Errorf(errorMsg.String())
	}

	return nil
}

func runMain() error {
	flag.Parse()

	// Validate command-line option conflicts
	if err := validateOptionConflicts(); err != nil {
		return err
	}

	// Check if we need sudo for device operations
	if needsSudo() && os.Getuid() != 0 {
		relaunchWithSudo()
		return nil
	}

	// Initialize JSON output if specified
	if err := initJSONOutput(); err != nil {
		return fmt.Errorf("failed to initialize JSON output: %v", err)
	}
	defer closeJSONOutput()

	if *version {
		if isJSONMode() {
			result := map[string]string{"version": Version}
			writeJSON(result)
		} else {
			fmt.Println(Version)
		}
		return nil
	}
	if *examples {
		printExamples()
		return nil
	}
	if len(os.Args) == 1 {
		return runNewInteractiveMenu()
	}
	if *resume != "" {
		return doResumeWithError(*resume)
	}
	if *wipe != "" {
		return doWipeWithError(*output, *wipe, *bs, *count, *progress, *dryRun)
	}
	if *input != "" && *output != "" {
		return doCopyWithError(*input, *output, *bs, *count, *skip, *seek, *compress, *decompress, *progress, *verify, *checksum)
	}
	return fmt.Errorf("invalid usage. Use -h for help or run with no arguments for interactive mode")
}

// ----------- Enhanced Terminal UI Framework -----------

// ANSI Color codes
const (
	ColorReset     = "\033[0m"
	ColorBold      = "\033[1m"
	ColorDim       = "\033[2m"
	ColorUnderline = "\033[4m"

	// Foreground colors
	ColorBlack   = "\033[30m"
	ColorRed     = "\033[31m"
	ColorGreen   = "\033[32m"
	ColorYellow  = "\033[33m"
	ColorBlue    = "\033[34m"
	ColorMagenta = "\033[35m"
	ColorCyan    = "\033[36m"
	ColorWhite   = "\033[37m"

	// Background colors
	ColorBgBlack   = "\033[40m"
	ColorBgRed     = "\033[41m"
	ColorBgGreen   = "\033[42m"
	ColorBgYellow  = "\033[43m"
	ColorBgBlue    = "\033[44m"
	ColorBgMagenta = "\033[45m"
	ColorBgCyan    = "\033[46m"
	ColorBgWhite   = "\033[47m"

	// Bright colors
	ColorBrightBlack   = "\033[90m"
	ColorBrightRed     = "\033[91m"
	ColorBrightGreen   = "\033[92m"
	ColorBrightYellow  = "\033[93m"
	ColorBrightBlue    = "\033[94m"
	ColorBrightMagenta = "\033[95m"
	ColorBrightCyan    = "\033[96m"
	ColorBrightWhite   = "\033[97m"
)

// Box drawing characters
const (
	BoxTopLeft     = "┌"
	BoxTopRight    = "┐"
	BoxBottomLeft  = "└"
	BoxBottomRight = "┘"
	BoxHorizontal  = "─"
	BoxVertical    = "│"
	BoxCross       = "┼"
	BoxTeeDown     = "┬"
	BoxTeeUp       = "┴"
	BoxTeeRight    = "├"
	BoxTeeLeft     = "┤"

	// Double line variants
	BoxDoubleHorizontal  = "═"
	BoxDoubleVertical    = "║"
	BoxDoubleTopLeft     = "╔"
	BoxDoubleTopRight    = "╗"
	BoxDoubleBottomLeft  = "╚"
	BoxDoubleBottomRight = "╝"
)

// Terminal control sequences
const (
	ClearScreen     = "\033[2J\033[H"
	ClearLine       = "\033[2K"
	SaveCursor      = "\033[s"
	RestoreCursor   = "\033[u"
	HideCursor      = "\033[?25l"
	ShowCursor      = "\033[?25h"
	MoveCursorUp    = "\033[A"
	MoveCursorDown  = "\033[B"
	MoveCursorRight = "\033[C"
	MoveCursorLeft  = "\033[D"
)

// UI Theme colors
var (
	ThemeTitle     = ColorBold + ColorBrightCyan
	ThemeSubtitle  = ColorBrightBlue
	ThemeHighlight = ColorBold + ColorBrightYellow
	ThemeSelected  = ColorBold + ColorBgBlue + ColorWhite
	ThemeNormal    = ColorWhite
	ThemeDisabled  = ColorBrightBlack
	ThemeSuccess   = ColorBrightGreen
	ThemeWarning   = ColorBrightYellow
	ThemeError     = ColorBrightRed
	ThemeInfo      = ColorBrightCyan
	ThemeBorder    = ColorBrightBlue
	ThemeProgress  = ColorBrightGreen
)

// Terminal UI state
type TerminalUI struct {
	width  int
	height int
}

// Initialize terminal UI
func newTerminalUI() *TerminalUI {
	ui := &TerminalUI{
		width:  80, // Default width
		height: 24, // Default height
	}
	ui.detectTerminalSize()
	return ui
}

// Detect terminal size
func (ui *TerminalUI) detectTerminalSize() {
	// Try to get terminal size using ANSI escape sequences
	fmt.Print("\033[s\033[999;999H\033[6n\033[u")
	// This is a simplified approach - in a real implementation you'd parse the response
	// For now, we'll use reasonable defaults
}

func clearScreen() {
	// Clear screen and move cursor to top-left
	fmt.Print("\033[2J") // Clear entire screen
	fmt.Print("\033[H")  // Move cursor to home position (1,1)
	fmt.Print("\033[3J") // Clear scrollback buffer (if supported)
}

// Draw the application header
func drawHeader() {
	fmt.Printf("\033[1;1H")
	fmt.Print(ThemeTitle + "╔══════════════════════════════════════════════════════════════════════════════╗" + ColorReset)
	fmt.Printf("\033[2;1H")
	fmt.Print(ThemeTitle + "║" + strings.Repeat(" ", 78) + "║" + ColorReset)
	fmt.Printf("\033[2;25H")
	fmt.Print(ThemeTitle + "Bella v" + Version + " - Advanced Data Copier" + ColorReset)
	fmt.Printf("\033[3;1H")
	fmt.Print(ThemeTitle + "╚══════════════════════════════════════════════════════════════════════════════╝" + ColorReset)
}

func hideCursor() {
	fmt.Print(HideCursor)
}

func showCursor() {
	fmt.Print(ShowCursor)
}

// Draw a box with title
func drawBox(x, y, width, height int, title string, style string) {
	// Move to position and draw top border
	fmt.Printf("\033[%d;%dH", y, x)
	fmt.Print(style + BoxTopLeft)

	// Title in the top border
	titleLen := len(title)
	if titleLen > 0 && titleLen < width-4 {
		padding := (width - titleLen - 4) / 2
		fmt.Print(strings.Repeat(BoxHorizontal, padding))
		fmt.Print("[ " + ThemeTitle + title + style + " ]")
		fmt.Print(strings.Repeat(BoxHorizontal, width-titleLen-4-padding))
	} else {
		fmt.Print(strings.Repeat(BoxHorizontal, width-2))
	}
	fmt.Print(BoxTopRight)

	// Draw sides
	for i := 1; i < height-1; i++ {
		fmt.Printf("\033[%d;%dH", y+i, x)
		fmt.Print(BoxVertical)
		fmt.Printf("\033[%d;%dH", y+i, x+width-1)
		fmt.Print(BoxVertical)
	}

	// Draw bottom border
	fmt.Printf("\033[%d;%dH", y+height-1, x)
	fmt.Print(BoxBottomLeft + strings.Repeat(BoxHorizontal, width-2) + BoxBottomRight)
	fmt.Print(ColorReset)
}

// Enhanced input handling with keyboard navigation
type KeyInput struct {
	Key     rune
	Special string // For special keys like arrows, escape, etc.
}

// Key constants
const (
	KeyEnter     = '\r'
	KeyEscape    = '\033'
	KeyBackspace = '\b'
	KeyTab       = '\t'
	KeySpace     = ' '

	// Special key sequences
	KeyArrowUp    = "UP"
	KeyArrowDown  = "DOWN"
	KeyArrowLeft  = "LEFT"
	KeyArrowRight = "RIGHT"
	KeyHome       = "HOME"
	KeyEnd        = "END"
	KeyPageUp     = "PGUP"
	KeyPageDown   = "PGDN"
	KeyDelete     = "DEL"
)

// Terminal state for raw mode
var originalTermios unix.Termios

// Read a single key with support for special keys
func readKey() KeyInput {
	var buf [6]byte // Increased buffer size for longer sequences
	n, _ := os.Stdin.Read(buf[:1])
	if n == 0 {
		return KeyInput{}
	}

	// Handle regular characters
	if buf[0] != 27 {
		return KeyInput{Key: rune(buf[0])}
	}

	// Handle ESC sequences
	// Set a short timeout for reading escape sequences
	os.Stdin.SetReadDeadline(time.Now().Add(100 * time.Millisecond))
	n, _ = os.Stdin.Read(buf[1:])
	os.Stdin.SetReadDeadline(time.Time{}) // Clear timeout

	if n == 0 {
		// Just ESC key pressed
		return KeyInput{Key: KeyEscape}
	}

	// Handle ANSI escape sequences
	if n >= 2 && buf[1] == '[' {
		switch buf[2] {
		case 'A':
			return KeyInput{Special: KeyArrowUp}
		case 'B':
			return KeyInput{Special: KeyArrowDown}
		case 'C':
			return KeyInput{Special: KeyArrowRight}
		case 'D':
			return KeyInput{Special: KeyArrowLeft}
		case 'H':
			return KeyInput{Special: KeyHome}
		case 'F':
			return KeyInput{Special: KeyEnd}
		case '1':
			// Handle sequences like [1~ (Home)
			if n >= 3 && buf[3] == '~' {
				return KeyInput{Special: KeyHome}
			}
		case '2':
			// Handle sequences like [2~ (Insert)
			if n >= 3 && buf[3] == '~' {
				return KeyInput{Special: "INSERT"}
			}
		case '3':
			// Handle sequences like [3~ (Delete)
			if n >= 3 && buf[3] == '~' {
				return KeyInput{Special: KeyDelete}
			}
		case '4':
			// Handle sequences like [4~ (End)
			if n >= 3 && buf[3] == '~' {
				return KeyInput{Special: KeyEnd}
			}
		case '5':
			// Handle sequences like [5~ (Page Up)
			if n >= 3 && buf[3] == '~' {
				return KeyInput{Special: KeyPageUp}
			}
		case '6':
			// Handle sequences like [6~ (Page Down)
			if n >= 3 && buf[3] == '~' {
				return KeyInput{Special: KeyPageDown}
			}
		}
	}

	// If we can't parse the sequence, treat as ESC
	return KeyInput{Key: KeyEscape}
}

// Set terminal to raw mode for better input handling
func setRawMode() error {
	fd := int(os.Stdin.Fd())

	// Get current terminal settings
	_, _, errno := syscall.Syscall(syscall.SYS_IOCTL, uintptr(fd), unix.TCGETS, uintptr(unsafe.Pointer(&originalTermios)))
	if errno != 0 {
		return fmt.Errorf("failed to get terminal attributes: %v", errno)
	}

	// Create new settings for raw mode
	raw := originalTermios
	raw.Lflag &^= unix.ECHO | unix.ICANON | unix.ISIG | unix.IEXTEN
	raw.Iflag &^= unix.BRKINT | unix.ICRNL | unix.INPCK | unix.ISTRIP | unix.IXON
	raw.Cflag &^= unix.CSIZE | unix.PARENB
	raw.Cflag |= unix.CS8
	raw.Oflag &^= unix.OPOST
	raw.Cc[unix.VMIN] = 1
	raw.Cc[unix.VTIME] = 0

	// Apply raw mode settings
	_, _, errno = syscall.Syscall(syscall.SYS_IOCTL, uintptr(fd), unix.TCSETS, uintptr(unsafe.Pointer(&raw)))
	if errno != 0 {
		return fmt.Errorf("failed to set terminal to raw mode: %v", errno)
	}

	return nil
}

func restoreTerminalMode() error {
	fd := int(os.Stdin.Fd())

	// Restore original terminal settings
	_, _, errno := syscall.Syscall(syscall.SYS_IOCTL, uintptr(fd), unix.TCSETS, uintptr(unsafe.Pointer(&originalTermios)))
	if errno != 0 {
		return fmt.Errorf("failed to restore terminal mode: %v", errno)
	}

	return nil
}

// Enhanced menu with keyboard navigation
type Menu struct {
	Title    string
	Items    []MenuItem
	Selected int
	X, Y     int
	Width    int
	Height   int
	ShowHelp bool
}

type MenuItem struct {
	Text        string
	Description string
	Action      func() error
	Enabled     bool
}

// Create a new menu
func newMenu(title string, x, y, width int) *Menu {
	return &Menu{
		Title:    title,
		Items:    make([]MenuItem, 0),
		Selected: 0,
		X:        x,
		Y:        y,
		Width:    width,
		ShowHelp: true,
	}
}

// Add menu item
func (m *Menu) addItem(text, description string, action func() error) {
	m.Items = append(m.Items, MenuItem{
		Text:        text,
		Description: description,
		Action:      action,
		Enabled:     true,
	})
}

// Draw the menu
func (m *Menu) draw() {
	// Calculate height based on items
	m.Height = len(m.Items) + 4 // Items + borders + padding
	if m.ShowHelp {
		m.Height += 3 // Help text
	}

	// Clear the menu area first
	for i := 0; i < m.Height+2; i++ { // +2 for description line
		fmt.Printf("\033[%d;%dH", m.Y+i, m.X)
		fmt.Print(strings.Repeat(" ", m.Width))
	}

	// Draw main box
	drawBox(m.X, m.Y, m.Width, m.Height, m.Title, ThemeBorder)

	// Draw menu items
	for i, item := range m.Items {
		y := m.Y + 2 + i
		x := m.X + 2

		// Highlight selected item
		if i == m.Selected {
			fmt.Printf("\033[%d;%dH", y, x)
			fmt.Print(ThemeSelected)
			fmt.Printf("%-*s", m.Width-4, fmt.Sprintf("%d. %s", i+1, item.Text))
			fmt.Print(ColorReset)
		} else {
			fmt.Printf("\033[%d;%dH", y, x)
			if item.Enabled {
				fmt.Print(ThemeNormal)
			} else {
				fmt.Print(ThemeDisabled)
			}
			fmt.Printf("%d. %s", i+1, item.Text)
			fmt.Print(ColorReset)
		}
	}

	// Draw help text
	if m.ShowHelp {
		helpY := m.Y + m.Height - 3
		fmt.Printf("\033[%d;%dH", helpY, m.X+2)
		fmt.Print(ThemeInfo + "↑↓: Navigate  Enter: Select  Esc: Back" + ColorReset)
	}

	// Show description for selected item
	if m.Selected < len(m.Items) && m.Items[m.Selected].Description != "" {
		descY := m.Y + m.Height + 1
		fmt.Printf("\033[%d;%dH", descY, m.X)
		fmt.Print(ThemeSubtitle + m.Items[m.Selected].Description + ColorReset)
	}
}

// Handle menu navigation and return selected action
func (m *Menu) run() error {
	// Try raw mode first for better navigation, fallback to line mode if it fails
	if err := m.runRawMode(); err != nil {
		// If raw mode fails, fallback to line mode
		return m.runLineMode()
	}
	return nil
}

// Raw mode menu with arrow key navigation
func (m *Menu) runRawMode() error {
	// Set terminal to raw mode
	if err := setRawMode(); err != nil {
		return err
	}
	defer restoreTerminalMode()

	// Initial draw
	clearScreen()
	drawHeader()
	m.draw()

	// Show help text
	fmt.Printf("\033[%d;%dH", m.Y+m.Height+3, m.X)
	fmt.Print(ThemeDisabled + "Use ↑↓ arrows to navigate, Enter to select, Esc/q to quit" + ColorReset)

	for {
		// Read key input
		key := readKey()

		switch {
		case key.Special == KeyArrowUp:
			if m.Selected > 0 {
				m.Selected--
			} else {
				m.Selected = len(m.Items) - 1 // Wrap to bottom
			}
			// Only redraw the menu items, not the entire screen
			m.draw()

		case key.Special == KeyArrowDown:
			if m.Selected < len(m.Items)-1 {
				m.Selected++
			} else {
				m.Selected = 0 // Wrap to top
			}
			// Only redraw the menu items, not the entire screen
			m.draw()

		case key.Key == KeyEnter || key.Key == '\n':
			if m.Selected < len(m.Items) && m.Items[m.Selected].Enabled && m.Items[m.Selected].Action != nil {
				return m.Items[m.Selected].Action()
			}

		case key.Key == KeyEscape || key.Key == 'q' || key.Key == 'Q':
			return nil

		case key.Key >= '1' && key.Key <= '9':
			// Allow numeric selection as well
			num := int(key.Key - '0')
			if num > 0 && num <= len(m.Items) {
				m.Selected = num - 1
				if m.Items[m.Selected].Enabled && m.Items[m.Selected].Action != nil {
					return m.Items[m.Selected].Action()
				}
			}
		}
	}
}

// Fallback line-based menu for when raw mode is not available
func (m *Menu) runLineMode() error {
	for {
		clearScreen()
		drawHeader()

		// Draw menu in line mode
		fmt.Printf("\n%s%s%s\n\n", ThemeTitle, m.Title, ColorReset)

		for i, item := range m.Items {
			if i == m.Selected {
				fmt.Printf("%s> %d. %s%s\n", ThemeSelected, i+1, item.Text, ColorReset)
			} else {
				fmt.Printf("  %d. %s\n", i+1, item.Text)
			}
		}

		fmt.Printf("\n%sEnter choice (1-%d), 'q' to quit: %s", ThemeHighlight, len(m.Items), ColorReset)

		input := readInput()

		switch input {
		case "q", "Q":
			return nil
		default:
			if num, err := strconv.Atoi(input); err == nil && num > 0 && num <= len(m.Items) {
				m.Selected = num - 1
				if m.Items[m.Selected].Enabled && m.Items[m.Selected].Action != nil {
					return m.Items[m.Selected].Action()
				}
			}
		}
	}
}

// Enhanced status bar with better positioning and formatting
func drawStatusBar(message string, statusType string) {
	// Save current cursor position
	fmt.Print(SaveCursor)

	// Move to bottom of screen
	fmt.Printf("\033[24;1H")
	fmt.Print(ClearLine)

	var color, icon string
	switch statusType {
	case "info":
		color = ThemeInfo
		icon = "ℹ"
	case "success":
		color = ThemeSuccess
		icon = "✓"
	case "warning":
		color = ThemeWarning
		icon = "⚠"
	case "error":
		color = ThemeError
		icon = "✗"
	default:
		color = ThemeNormal
		icon = "•"
	}

	// Format status message with icon and timestamp
	timestamp := time.Now().Format("15:04:05")
	statusText := fmt.Sprintf("%s %s %s %s[%s]%s",
		color, icon, message, ThemeDisabled, timestamp, ColorReset)

	fmt.Print(statusText)

	// Restore cursor position
	fmt.Print(RestoreCursor)
}

// Draw help bar at the bottom with context-sensitive shortcuts
func drawHelpBar(shortcuts map[string]string) {
	// Save current cursor position
	fmt.Print(SaveCursor)

	// Move to second-to-last line
	fmt.Printf("\033[23;1H")
	fmt.Print(ClearLine)

	// Draw separator line
	fmt.Print(ThemeBorder + strings.Repeat("─", 80) + ColorReset)

	// Move to last line for help text
	fmt.Printf("\033[24;1H")
	fmt.Print(ClearLine)

	// Format shortcuts
	var helpItems []string
	for key, desc := range shortcuts {
		helpItems = append(helpItems, fmt.Sprintf("%s%s%s:%s",
			ThemeHighlight, key, ThemeNormal, desc))
	}

	helpText := strings.Join(helpItems, "  ")
	fmt.Print(helpText + ColorReset)

	// Restore cursor position
	fmt.Print(RestoreCursor)
}

// Show context-sensitive help overlay
func showHelpOverlay(title string, helpItems []HelpItem) {
	clearScreen()

	// Calculate dialog dimensions
	maxWidth := 0
	for _, item := range helpItems {
		itemWidth := len(item.Key) + len(item.Description) + 4
		if itemWidth > maxWidth {
			maxWidth = itemWidth
		}
	}

	dialogWidth := maxWidth + 4
	if dialogWidth < 50 {
		dialogWidth = 50
	}
	if dialogWidth > 76 {
		dialogWidth = 76
	}

	dialogHeight := len(helpItems) + 6
	x := (80 - dialogWidth) / 2
	y := (24 - dialogHeight) / 2

	// Draw help dialog
	drawBox(x, y, dialogWidth, dialogHeight, title, ThemeInfo)

	// Draw help items
	for i, item := range helpItems {
		itemY := y + 2 + i
		fmt.Printf("\033[%d;%dH", itemY, x+2)
		fmt.Printf("%s%-12s%s %s",
			ThemeHighlight, item.Key, ThemeNormal, item.Description)
	}

	// Draw footer
	fmt.Printf("\033[%d;%dH", y+dialogHeight-2, x+2)
	fmt.Print(ThemeDisabled + "Press any key to continue..." + ColorReset)

	readKey()
}

type HelpItem struct {
	Key         string
	Description string
}

// Enhanced progress bar with colors
func drawEnhancedProgressBar(percent float64, width int, label string) string {
	filled := int(percent * float64(width) / 100.0)
	bar := ThemeProgress + strings.Repeat("█", filled) +
		ThemeDisabled + strings.Repeat("░", width-filled) + ColorReset

	return fmt.Sprintf("%s [%s] %.1f%%", label, bar, percent)
}

// Create a confirmation dialog
func showConfirmDialog(title, message string, defaultYes bool) bool {
	clearScreen()

	// Calculate dialog dimensions
	dialogWidth := 60
	dialogHeight := 8
	x := (80 - dialogWidth) / 2
	y := (24 - dialogHeight) / 2

	// Draw dialog box
	drawBox(x, y, dialogWidth, dialogHeight, title, ThemeWarning)

	// Draw message
	fmt.Printf("\033[%d;%dH", y+2, x+2)
	fmt.Print(ThemeNormal + message + ColorReset)

	// Draw options
	fmt.Printf("\033[%d;%dH", y+4, x+2)
	if defaultYes {
		fmt.Print(ThemeHighlight + "[Y]es" + ThemeNormal + " / [N]o" + ColorReset)
	} else {
		fmt.Print(ThemeNormal + "[Y]es / " + ThemeHighlight + "[N]o" + ColorReset)
	}

	// Set raw mode for input
	setRawMode()
	defer restoreTerminalMode()

	// Wait for input
	for {
		key := readKey()
		switch key.Key {
		case 'y', 'Y':
			return true
		case 'n', 'N':
			return false
		case KeyEnter, '\n':
			return defaultYes
		case KeyEscape:
			return false
		}
	}
}

// Enhanced input dialog
func showInputDialog(title, prompt, defaultValue string) string {
	clearScreen()

	// Calculate dialog dimensions
	dialogWidth := 60
	dialogHeight := 6
	x := (80 - dialogWidth) / 2
	y := (24 - dialogHeight) / 2

	// Draw dialog box
	drawBox(x, y, dialogWidth, dialogHeight, title, ThemeInfo)

	// Draw prompt
	fmt.Printf("\033[%d;%dH", y+2, x+2)
	fmt.Print(ThemeNormal + prompt + ColorReset)

	// Show default value
	fmt.Printf("\033[%d;%dH", y+3, x+2)
	fmt.Print(ThemeSubtitle + "Default: " + defaultValue + ColorReset)

	// Input field
	fmt.Printf("\033[%d;%dH", y+4, x+2)
	fmt.Print(ThemeHighlight + "> " + ColorReset)
	showCursor()

	input := readInput()
	if input == "" {
		input = defaultValue
	}

	hideCursor()
	return input
}

// runInteractiveMenu returns an error instead of calling os.Exit
func runInteractiveMenu() error {
	clearScreen()

	// Create main menu (positioned below header)
	menu := newMenu("Bella - Interactive Data Copier", 10, 5, 60)

	menu.addItem("Copy", "Copy data between files and devices with advanced options", func() error {
		return runEnhancedCopyMenu()
	})

	menu.addItem("Wipe", "Securely wipe devices or files with zero or random data", func() error {
		return runEnhancedWipeMenu()
	})

	menu.addItem("Verify", "Compare two files or devices for data integrity", func() error {
		return runEnhancedVerifyMenu()
	})

	menu.addItem("Device Info", "Display detailed information about storage devices", func() error {
		return runEnhancedDeviceInfoMenu()
	})

	menu.addItem("Show Examples", "Display usage examples and command-line options", func() error {
		clearScreen()
		printExamples()
		drawStatusBar("Press any key to continue...", "info")
		readKey()
		return nil
	})

	menu.addItem("Exit", "Exit the Bella application", func() error {
		if showConfirmDialog("Confirm Exit", "Are you sure you want to exit Bella?", false) {
			return fmt.Errorf("exit")
		}
		return nil
	})

	// Draw header
	drawHeader()

	// Run menu loop
	for {
		err := menu.run()
		if err != nil {
			if err.Error() == "exit" {
				return nil
			}
			drawStatusBar("Error: "+err.Error(), "error")
			readKey() // Wait for user acknowledgment
			// Only clear and redraw after error acknowledgment
			clearScreen()
			drawHeader()
		}
		// Don't clear screen on normal menu returns - let the menu handle its own display
	}
}

// Legacy interactiveMenu function for backward compatibility
func interactiveMenu() {
	if err := runInteractiveMenu(); err != nil {
		fmt.Fprintf(os.Stderr, "Interactive menu error: %v\n", err)
		os.Exit(1)
	}
}

// Native input reading function
func readInput() string {
	reader := bufio.NewReader(os.Stdin)
	input, _ := reader.ReadString('\n')
	return strings.TrimSpace(input)
}

// Native prompt functions
func promptInput(label string) string {
	fmt.Printf("%s: ", label)
	return readInput()
}

func promptInt(label string, def int) int {
	fmt.Printf("%s (default %d): ", label, def)
	input := readInput()
	if input == "" {
		return def
	}
	val, err := strconv.Atoi(input)
	if err != nil {
		fmt.Printf("Invalid number, using default %d\n", def)
		return def
	}
	return val
}

func promptInt64(label string, def int64) int64 {
	fmt.Printf("%s (default %d): ", label, def)
	input := readInput()
	if input == "" {
		return def
	}
	val, err := strconv.ParseInt(input, 10, 64)
	if err != nil {
		fmt.Printf("Invalid number, using default %d\n", def)
		return def
	}
	return val
}

func promptSelect(label string, items []string) string {
	fmt.Printf("%s:\n", label)
	for i, item := range items {
		fmt.Printf("%d. %s\n", i+1, item)
	}
	fmt.Print("Enter choice: ")
	input := readInput()
	choice, err := strconv.Atoi(input)
	if err != nil || choice < 1 || choice > len(items) {
		fmt.Printf("Invalid choice, using first option: %s\n", items[0])
		return items[0]
	}
	return items[choice-1]
}

func promptConfirm(label string, def bool) bool {
	defaultStr := "y"
	if !def {
		defaultStr = "n"
	}
	fmt.Printf("%s (y/n, default %s): ", label, defaultStr)
	input := readInput()
	if input == "" {
		return def
	}
	return strings.ToLower(input) == "y" || strings.ToLower(input) == "yes"
}

// ----------- Linux-specific device detection -----------

func detectDevices() ([]DeviceInfo, error) {
	var devices []DeviceInfo

	// Read /proc/partitions for block devices
	partitions, err := readProcPartitions()
	if err != nil {
		return nil, err
	}

	for _, partition := range partitions {
		device := DeviceInfo{
			Path: "/dev/" + partition.Name,
			Name: partition.Name,
			Size: partition.Size * 1024, // Convert from KB to bytes
		}

		// Get additional device information
		enrichDeviceInfo(&device)
		devices = append(devices, device)
	}

	return devices, nil
}

type Partition struct {
	Major  int
	Minor  int
	Blocks int64
	Name   string
	Size   int64
}

func readProcPartitions() ([]Partition, error) {
	file, err := os.Open("/proc/partitions")
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var partitions []Partition
	scanner := bufio.NewScanner(file)

	// Skip header lines
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "major") {
			continue
		}

		fields := strings.Fields(line)
		if len(fields) < 4 {
			continue
		}

		major, _ := strconv.Atoi(fields[0])
		minor, _ := strconv.Atoi(fields[1])
		blocks, _ := strconv.ParseInt(fields[2], 10, 64)
		name := fields[3]

		partitions = append(partitions, Partition{
			Major:  major,
			Minor:  minor,
			Blocks: blocks,
			Name:   name,
			Size:   blocks,
		})
	}

	return partitions, scanner.Err()
}

func enrichDeviceInfo(device *DeviceInfo) {
	// Get filesystem information from /proc/mounts
	device.Filesystem, device.Mountpoint = getFilesystemInfo(device.Name)

	// Get device properties from /sys/block
	getSysBlockInfo(device)

	// Get block size
	device.BlockSize = getBlockSize(device.Path)
}

func getFilesystemInfo(deviceName string) (string, string) {
	file, err := os.Open("/proc/mounts")
	if err != nil {
		return "", ""
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		fields := strings.Fields(scanner.Text())
		if len(fields) >= 3 {
			if strings.Contains(fields[0], deviceName) {
				return fields[2], fields[1] // filesystem type, mountpoint
			}
		}
	}
	return "", ""
}

func getSysBlockInfo(device *DeviceInfo) {
	baseName := device.Name
	// Remove partition number for base device
	re := regexp.MustCompile(`\d+$`)
	baseName = re.ReplaceAllString(baseName, "")

	sysPath := "/sys/block/" + baseName

	// Check if device is removable
	if data, err := os.ReadFile(sysPath + "/removable"); err == nil {
		device.Removable = strings.TrimSpace(string(data)) == "1"
	}

	// Check if device is read-only
	if data, err := os.ReadFile(sysPath + "/ro"); err == nil {
		device.ReadOnly = strings.TrimSpace(string(data)) == "1"
	}

	// Check if device is rotational
	if data, err := os.ReadFile(sysPath + "/queue/rotational"); err == nil {
		device.Rotational = strings.TrimSpace(string(data)) == "1"
	}

	// Get device model
	if data, err := os.ReadFile(sysPath + "/device/model"); err == nil {
		device.Model = strings.TrimSpace(string(data))
	}

	// Get device vendor
	if data, err := os.ReadFile(sysPath + "/device/vendor"); err == nil {
		device.Vendor = strings.TrimSpace(string(data))
	}
}

func getBlockSize(devicePath string) int64 {
	file, err := os.Open(devicePath)
	if err != nil {
		return 512 // Default block size
	}
	defer file.Close()

	// Use ioctl to get block size
	fd := int(file.Fd())

	// BLKSSZGET ioctl to get logical block size using unix package
	blockSize, err := unix.IoctlGetInt(fd, unix.BLKSSZGET)
	if err != nil {
		return 512 // Default if ioctl fails
	}

	return int64(blockSize)
}

// Get device size using ioctl
func getDeviceSize(devicePath string) int64 {
	file, err := os.Open(devicePath)
	if err != nil {
		return 0
	}
	defer file.Close()

	fd := int(file.Fd())

	// BLKGETSIZE64 ioctl to get device size in bytes using unix package
	var size uint64
	_, _, errno := unix.Syscall(unix.SYS_IOCTL, uintptr(fd), unix.BLKGETSIZE64, uintptr(unsafe.Pointer(&size)))
	if errno != 0 {
		// Fallback: try to seek to end to get size
		if currentPos, err := file.Seek(0, io.SeekCurrent); err == nil {
			if endPos, err := file.Seek(0, io.SeekEnd); err == nil {
				file.Seek(currentPos, io.SeekStart) // Restore position
				return endPos
			}
		}
		return 0
	}

	return int64(size)
}

// ----------- Skip Empty Space Feature -----------

// PartitionInfo represents a partition on a device
type PartitionInfo struct {
	Name      string
	StartByte int64
	EndByte   int64
	SizeBytes int64
}

// PartitionRegion represents a data region for partition-only copying
type PartitionRegion struct {
	StartByte int64
	EndByte   int64
	SizeBytes int64
	Name      string
	Type      string
}

// PartitionTable represents the complete partition table information
type PartitionTable struct {
	Device     string
	SectorSize int64
	Regions    []PartitionRegion
	TotalSize  int64
}

// getLastPartitionEnd finds the end of the last partition plus a buffer using native parsing
func getLastPartitionEnd(devicePath string) (int64, error) {
	// Parse partition table natively
	partitionTable, err := parsePartitionTable(devicePath)
	if err != nil {
		return 0, fmt.Errorf("failed to parse partition table: %v", err)
	}

	var lastEnd int64 = 0

	// Find the last partition end (skip header and backup regions)
	for _, region := range partitionTable.Regions {
		if region.Type != "header" && region.Type != "backup" {
			if region.EndByte > lastEnd {
				lastEnd = region.EndByte
			}
		}
	}

	if lastEnd == 0 {
		return 0, fmt.Errorf("no partitions found on device %s", devicePath)
	}

	// Add 2MB buffer for boot sectors, etc.
	buffer := int64(2 * 1024 * 1024)
	return lastEnd + buffer, nil
}

// calculateSkipEmptyCount calculates the block count for skipempty mode
func calculateSkipEmptyCount(inputPath string, blockSize int) (int, error) {
	lastEnd, err := getLastPartitionEnd(inputPath)
	if err != nil {
		return -1, err
	}

	// Calculate number of blocks needed
	blockCount := int(lastEnd / int64(blockSize))
	if lastEnd%int64(blockSize) != 0 {
		blockCount++ // Round up
	}

	fmt.Printf("Skip-empty mode: copying up to byte %d (%s) in %d blocks\n",
		lastEnd, humanizeBytes(uint64(lastEnd)), blockCount)

	return blockCount, nil
}

// ----------- Partition-Only Backup Feature -----------

// Native partition table structures
type MBRPartitionEntry struct {
	Status   uint8
	StartCHS [3]uint8
	Type     uint8
	EndCHS   [3]uint8
	StartLBA uint32
	SizeLBA  uint32
}

type GPTHeader struct {
	Signature           [8]byte
	Revision            uint32
	HeaderSize          uint32
	HeaderCRC32         uint32
	Reserved            uint32
	CurrentLBA          uint64
	BackupLBA           uint64
	FirstUsableLBA      uint64
	LastUsableLBA       uint64
	DiskGUID            [16]byte
	PartitionArrayLBA   uint64
	PartitionCount      uint32
	PartitionEntrySize  uint32
	PartitionArrayCRC32 uint32
}

type GPTPartitionEntry struct {
	TypeGUID      [16]byte
	PartitionGUID [16]byte
	StartLBA      uint64
	EndLBA        uint64
	Attributes    uint64
	Name          [72]byte // UTF-16LE
}

// parsePartitionTable parses the partition table natively and returns regions to copy
func parsePartitionTable(devicePath string) (*PartitionTable, error) {
	file, err := os.Open(devicePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open device: %v", err)
	}
	defer file.Close()

	// Read first sector to determine partition table type
	sector := make([]byte, 512)
	if _, err := file.ReadAt(sector, 0); err != nil {
		return nil, fmt.Errorf("failed to read first sector: %v", err)
	}

	// Check for GPT signature
	if string(sector[0:8]) == "EFI PART" {
		return parseGPTPartitionTable(file, devicePath)
	}

	// Check for MBR signature
	if sector[510] == 0x55 && sector[511] == 0xAA {
		return parseMBRPartitionTable(file, devicePath, sector)
	}

	return nil, fmt.Errorf("no valid partition table found")
}

// parseMBRPartitionTable parses MBR partition table
func parseMBRPartitionTable(file *os.File, devicePath string, sector []byte) (*PartitionTable, error) {
	pt := &PartitionTable{
		Device:     devicePath,
		SectorSize: 512,
		Regions:    make([]PartitionRegion, 0),
	}

	// Add MBR header region (first 2MB for safety)
	headerSize := int64(2 * 1024 * 1024)
	pt.Regions = append(pt.Regions, PartitionRegion{
		StartByte: 0,
		EndByte:   headerSize,
		SizeBytes: headerSize,
		Name:      "mbr-header",
		Type:      "header",
	})

	// Parse MBR partition entries (4 entries starting at offset 446)
	for i := 0; i < 4; i++ {
		offset := 446 + (i * 16)
		if offset+16 > len(sector) {
			break
		}

		entry := MBRPartitionEntry{
			Status:   sector[offset],
			StartLBA: binary.LittleEndian.Uint32(sector[offset+8 : offset+12]),
			SizeLBA:  binary.LittleEndian.Uint32(sector[offset+12 : offset+16]),
			Type:     sector[offset+4],
		}

		// Skip empty partitions
		if entry.SizeLBA == 0 || entry.Type == 0 {
			continue
		}

		startByte := int64(entry.StartLBA) * pt.SectorSize
		sizeBytes := int64(entry.SizeLBA) * pt.SectorSize
		endByte := startByte + sizeBytes

		region := PartitionRegion{
			StartByte: startByte,
			EndByte:   endByte,
			SizeBytes: sizeBytes,
			Name:      fmt.Sprintf("%s%d", devicePath, i+1),
			Type:      fmt.Sprintf("mbr-type-%02x", entry.Type),
		}

		pt.Regions = append(pt.Regions, region)

		if endByte > pt.TotalSize {
			pt.TotalSize = endByte
		}
	}

	return pt, nil
}

// parseGPTPartitionTable parses GPT partition table
func parseGPTPartitionTable(file *os.File, devicePath string) (*PartitionTable, error) {
	// Read GPT header from sector 1 (LBA 1)
	headerBytes := make([]byte, 512)
	if _, err := file.ReadAt(headerBytes, 512); err != nil {
		return nil, fmt.Errorf("failed to read GPT header: %v", err)
	}

	// Parse GPT header
	header := GPTHeader{}
	buf := bytes.NewReader(headerBytes)
	if err := binary.Read(buf, binary.LittleEndian, &header); err != nil {
		return nil, fmt.Errorf("failed to parse GPT header: %v", err)
	}

	// Verify GPT signature
	if string(header.Signature[:]) != "EFI PART" {
		return nil, fmt.Errorf("invalid GPT signature")
	}

	pt := &PartitionTable{
		Device:     devicePath,
		SectorSize: 512,
		Regions:    make([]PartitionRegion, 0),
	}

	// Add GPT header region (first 2MB for safety)
	headerSize := int64(2 * 1024 * 1024)
	pt.Regions = append(pt.Regions, PartitionRegion{
		StartByte: 0,
		EndByte:   headerSize,
		SizeBytes: headerSize,
		Name:      "gpt-header",
		Type:      "header",
	})

	// Read partition entries
	partitionArrayOffset := int64(header.PartitionArrayLBA) * pt.SectorSize
	partitionArraySize := int64(header.PartitionCount) * int64(header.PartitionEntrySize)

	partitionData := make([]byte, partitionArraySize)
	if _, err := file.ReadAt(partitionData, partitionArrayOffset); err != nil {
		return nil, fmt.Errorf("failed to read partition entries: %v", err)
	}

	// Parse each partition entry
	for i := uint32(0); i < header.PartitionCount; i++ {
		offset := int64(i) * int64(header.PartitionEntrySize)
		if offset+int64(header.PartitionEntrySize) > int64(len(partitionData)) {
			break
		}

		entryBytes := partitionData[offset : offset+int64(header.PartitionEntrySize)]
		entry := GPTPartitionEntry{}

		buf := bytes.NewReader(entryBytes)
		if err := binary.Read(buf, binary.LittleEndian, &entry); err != nil {
			continue
		}

		// Skip empty partitions (all zeros in type GUID)
		isEmpty := true
		for _, b := range entry.TypeGUID {
			if b != 0 {
				isEmpty = false
				break
			}
		}
		if isEmpty {
			continue
		}

		startByte := int64(entry.StartLBA) * pt.SectorSize
		endByte := int64(entry.EndLBA+1) * pt.SectorSize // EndLBA is inclusive
		sizeBytes := endByte - startByte

		// Convert UTF-16LE name to string
		name := utf16LEToString(entry.Name[:])
		if name == "" {
			name = fmt.Sprintf("%s%d", devicePath, i+1)
		}

		region := PartitionRegion{
			StartByte: startByte,
			EndByte:   endByte,
			SizeBytes: sizeBytes,
			Name:      name,
			Type:      "gpt-partition",
		}

		pt.Regions = append(pt.Regions, region)

		if endByte > pt.TotalSize {
			pt.TotalSize = endByte
		}
	}

	// Add backup GPT region at the end (last 1MB for safety)
	if len(pt.Regions) > 1 { // Only if we have actual partitions
		backupStart := pt.TotalSize
		backupSize := int64(1024 * 1024)
		pt.Regions = append(pt.Regions, PartitionRegion{
			StartByte: backupStart,
			EndByte:   backupStart + backupSize,
			SizeBytes: backupSize,
			Name:      "backup-gpt",
			Type:      "backup",
		})
		pt.TotalSize = backupStart + backupSize
	}

	return pt, nil
}

// utf16LEToString converts UTF-16LE byte array to string
func utf16LEToString(data []byte) string {
	if len(data)%2 != 0 {
		return ""
	}

	u16s := make([]uint16, len(data)/2)
	for i := 0; i < len(u16s); i++ {
		u16s[i] = binary.LittleEndian.Uint16(data[i*2 : i*2+2])
	}

	// Find null terminator
	for i, r := range u16s {
		if r == 0 {
			u16s = u16s[:i]
			break
		}
	}

	return string(utf16.Decode(u16s))
}

// copyPartitionOnly performs partition-only backup
func copyPartitionOnly(inputPath, outputPath string, bs int, progress bool, throttleController *ThrottleController, hasher hash.Hash, result *OperationResult) error {
	// Parse partition table
	partitionTable, err := parsePartitionTable(inputPath)
	if err != nil {
		return fmt.Errorf("failed to parse partition table: %v", err)
	}

	if len(partitionTable.Regions) == 0 {
		return fmt.Errorf("no partitions found on device %s", inputPath)
	}

	// Open input file
	inFile, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input: %v", err)
	}
	defer inFile.Close()

	// Open output file
	outFile, err := os.OpenFile(outputPath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
	if err != nil {
		return fmt.Errorf("failed to open output: %v", err)
	}
	defer outFile.Close()

	// Calculate total size to copy
	var totalSize int64
	for _, region := range partitionTable.Regions {
		totalSize += region.SizeBytes
	}

	// Initialize progress reporter
	var progressReporter *ProgressReporter
	if progress {
		progressReporter = NewProgressReporter("partition-copy", isJSONMode())
	}

	// Initialize bad sector tracker
	var badSectorTracker *BadSectorTracker
	if *skipBadSectors {
		badSectorTracker = NewBadSectorTracker()
	}

	buf := make([]byte, bs)
	var totalCopied int64
	var totalBlocks int

	fmt.Printf("Partition-only mode: copying %d regions totaling %s\n",
		len(partitionTable.Regions), humanizeBytes(uint64(totalSize)))

	// Copy each region
	for i, region := range partitionTable.Regions {
		fmt.Printf("Copying region %d/%d: %s (%s at offset %s)\n",
			i+1, len(partitionTable.Regions), region.Name,
			humanizeBytes(uint64(region.SizeBytes)),
			humanizeBytes(uint64(region.StartByte)))

		// Seek to region start in input
		if _, err := inFile.Seek(region.StartByte, io.SeekStart); err != nil {
			return fmt.Errorf("failed to seek to region %s: %v", region.Name, err)
		}

		// Copy this region
		var regionCopied int64
		for regionCopied < region.SizeBytes {
			// Calculate how much to read
			toRead := bs
			if regionCopied+int64(toRead) > region.SizeBytes {
				toRead = int(region.SizeBytes - regionCopied)
			}

			// Read from input
			n, err := inFile.Read(buf[:toRead])
			if n > 0 {
				data := buf[:n]

				// Handle bad sectors if enabled
				if err != nil && badSectorTracker != nil {
					if zeroBuffer, shouldSkip := handleReadError(err, int64(totalBlocks), totalCopied, n, badSectorTracker); shouldSkip {
						data = zeroBuffer
						err = nil // Clear the error since we're handling it
					}
				}

				// Write to output
				if err == nil {
					wn, werr := outFile.Write(data)
					if werr != nil || wn != n {
						return fmt.Errorf("write error in region %s: %v", region.Name, werr)
					}

					// Update hash
					if hasher != nil {
						hasher.Write(data)
					}

					regionCopied += int64(n)
					totalCopied += int64(n)
					totalBlocks++

					// Throttling
					if throttleController != nil {
						throttleController.throttle(int64(n))
					}

					// Progress reporting
					if progressReporter != nil && totalBlocks%128 == 0 {
						progressReporter.Update(totalCopied, totalSize, totalBlocks)
					}
				}
			}

			if err == io.EOF {
				break
			}
			if err != nil && badSectorTracker == nil {
				return fmt.Errorf("read error in region %s: %v", region.Name, err)
			}
		}
	}

	// Final progress update
	if progressReporter != nil {
		progressReporter.Finish(totalCopied, totalSize, totalBlocks)
	}

	result.BytesCopied = totalCopied
	result.BlocksCopied = totalBlocks

	// Add bad sector information if applicable
	if badSectorTracker != nil {
		result.BadSectors = badSectorTracker.GetBadSectors()
		result.BadSectorCount = badSectorTracker.GetTotalBadSectors()
	}

	fmt.Printf("Partition-only copy completed: %s in %d regions\n",
		humanizeBytes(uint64(totalCopied)), len(partitionTable.Regions))

	return nil
}

// ----------- Sudo Helper Functions -----------

func needsSudo() bool {
	// Check if we're dealing with device files that need root access
	if *input != "" && strings.HasPrefix(*input, "/dev/") {
		return !canAccessDevice(*input)
	}
	if *output != "" && strings.HasPrefix(*output, "/dev/") {
		return !canAccessDevice(*output)
	}
	if *wipe != "" {
		return !canAccessDevice(*wipe)
	}
	return false
}

// canAccessDevice tests if we can actually access a device file
func canAccessDevice(devicePath string) bool {
	// First check if the device exists
	if _, err := os.Stat(devicePath); err != nil {
		return false
	}

	// Try to open the device for reading
	file, err := os.OpenFile(devicePath, os.O_RDONLY, 0)
	if err != nil {
		return false
	}
	file.Close()

	// For output devices, also check write access
	if devicePath == *output || devicePath == *wipe {
		file, err := os.OpenFile(devicePath, os.O_WRONLY, 0)
		if err != nil {
			return false
		}
		file.Close()
	}

	return true
}

// checkDeviceAccessAndElevate checks if we can access a device and elevates if needed
func checkDeviceAccessAndElevate(devicePath string) bool {
	if !strings.HasPrefix(devicePath, "/dev/") {
		return true // Not a device, no elevation needed
	}

	if canAccessDevice(devicePath) {
		return true // Already have access
	}

	if os.Getuid() == 0 {
		return false // Already root but still can't access - real error
	}

	// Need to elevate - relaunch with sudo
	fmt.Printf("Device %s requires root privileges. Relaunching with sudo...\n", devicePath)

	// Get the current executable path
	executable, err := os.Executable()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error getting executable path: %v\n", err)
		return false
	}

	// Prepare sudo command with all original arguments
	args := []string{"sudo", executable}
	args = append(args, os.Args[1:]...)

	// Execute sudo command
	cmd := exec.Command("sudo", args[1:]...)
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	err = cmd.Run()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error running with sudo: %v\n", err)
		return false
	}

	// If we get here, the sudo command completed, so we should exit
	os.Exit(0)
	return false // This line will never be reached, but satisfies the compiler
}

func relaunchWithSudo() {
	// Check if sudo is available
	if _, err := exec.LookPath("sudo"); err != nil {
		fmt.Fprintf(os.Stderr, "Error: sudo is required for device operations but not found in PATH\n")
		os.Exit(1)
	}

	fmt.Println("This operation requires root privileges. Relaunching with sudo...")

	// Get the current executable path
	executable, err := os.Executable()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error getting executable path: %v\n", err)
		os.Exit(1)
	}

	// Prepare sudo command with all original arguments
	args := []string{"sudo", executable}
	args = append(args, os.Args[1:]...)

	// Execute sudo command
	cmd := exec.Command("sudo", args[1:]...)
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	err = cmd.Run()
	if err != nil {
		// Check if it's a permission denied error
		if exitError, ok := err.(*exec.ExitError); ok {
			os.Exit(exitError.ExitCode())
		}
		fmt.Fprintf(os.Stderr, "Error running with sudo: %v\n", err)
		os.Exit(1)
	}
}

// ----------- JSON Output Helper -----------

var jsonManager *JSONManager

func isJSONMode() bool {
	return *jsonOutput != ""
}

func initJSONOutput() error {
	if !isJSONMode() {
		return nil
	}

	tempDir := getTempDirectory()
	userDir := ""
	if filepath.IsAbs(*jsonOutput) {
		userDir = filepath.Dir(*jsonOutput)
		*jsonOutput = filepath.Base(*jsonOutput)
	}

	jsonManager = NewJSONManager(*jsonOutput, *jsonMemory, tempDir, userDir)
	return nil
}

func closeJSONOutput() {
	if jsonManager != nil {
		if err := jsonManager.Close(); err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Failed to close JSON output: %v\n", err)
		}
	}
}

func writeJSON(data interface{}) {
	if isJSONMode() && jsonManager != nil {
		jsonData, err := json.Marshal(data)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Failed to marshal JSON data: %v\n", err)
			return
		}
		jsonData = append(jsonData, '\n') // Add newline for readability
		if err := jsonManager.Write(jsonData); err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Failed to write JSON data: %v\n", err)
		}
	}
}

// ----------- Menu Functions -----------

// isCompressedFile checks if a file is compressed based on extension or content
func isCompressedFile(filePath string) bool {
	// Check by file extension first
	ext := strings.ToLower(filepath.Ext(filePath))
	switch ext {
	case ".gz", ".gzip":
		return true
	case ".bz2", ".bzip2":
		return true
	case ".xz":
		return true
	case ".lz4":
		return true
	case ".zst", ".zstd":
		return true
	}

	// For device files, not compressed
	if strings.HasPrefix(filePath, "/dev/") {
		return false
	}

	// Check file content for gzip magic number if file exists
	if file, err := os.Open(filePath); err == nil {
		defer file.Close()
		header := make([]byte, 2)
		if n, err := file.Read(header); err == nil && n == 2 {
			// Check for gzip magic number (0x1f, 0x8b)
			if header[0] == 0x1f && header[1] == 0x8b {
				return true
			}
		}
	}

	return false
}

// checkOverwriteConfirmation checks if output file exists and prompts for confirmation
func checkOverwriteConfirmation(outputPath string) bool {
	// Skip confirmation for device files
	if strings.HasPrefix(outputPath, "/dev/") {
		return true
	}

	// Check if file exists
	if _, err := os.Stat(outputPath); err != nil {
		// File doesn't exist, no confirmation needed
		return true
	}

	// File exists, ask for confirmation
	fmt.Printf("Output file '%s' already exists.\n", outputPath)
	return promptConfirm("Overwrite existing file?", false)
}

func runCopyMenu() {
	clearScreen()
	in := selectDeviceOrFile("Select input file/device", true)
	if in == "Back" {
		return
	}
	clearScreen()
	out := selectDeviceOrFile("Select output file/device", false)
	if out == "Back" {
		return
	}
	bs := promptInt("Block size (bytes, e.g. 4096)", 4096)
	cnt := promptInt("Block count (-1 for all)", -1)
	skip := promptInt64("Blocks to skip at input", 0)
	seek := promptInt64("Blocks to seek at output", 0)
	sparse := promptConfirm("Enable sparse file support?", false)

	// Smart compression/decompression logic
	var compress, decompress string
	inputIsCompressed := isCompressedFile(in)
	outputShouldBeCompressed := isCompressedFile(out)

	if inputIsCompressed && !outputShouldBeCompressed {
		// Input is compressed, output is not - ask for decompression
		fmt.Printf("Input file appears to be compressed (%s)\n", in)
		decompress = promptSelect("Decompression method", []string{"auto", "gzip", "none"})
		compress = "none"
	} else if !inputIsCompressed && outputShouldBeCompressed {
		// Input is not compressed, output should be - ask for compression
		fmt.Printf("Output file extension suggests compression (%s)\n", out)
		compress = promptSelect("Compression level", []string{"gzip1", "gzip6", "gzip9"})
		decompress = "auto"
	} else if inputIsCompressed && outputShouldBeCompressed {
		// Both compressed - ask for both but with smart defaults
		fmt.Printf("Both input and output appear to be compressed\n")
		decompress = promptSelect("Input decompression", []string{"auto", "gzip", "none"})
		compress = promptSelect("Output compression", []string{"gzip1", "gzip6", "gzip9"})
	} else {
		// Neither compressed - ask if user wants compression
		if promptConfirm("Enable compression for output?", false) {
			compress = promptSelect("Compression level", []string{"gzip1", "gzip6", "gzip9"})
		} else {
			compress = "none"
		}
		decompress = "auto"
	}

	progress := promptConfirm("Show progress?", true)
	verify := promptConfirm("Verify after copy?", false)
	checksum := promptSelect("Checksum algorithm", []string{"none", "sha256", "sha512", "md5"})

	// Show warning if sparse is enabled with compression
	if sparse && compress != "none" {
		fmt.Println("\nNote: Sparse optimization will be disabled when compression is used.")
	}

	// Check for overwrite confirmation
	if !checkOverwriteConfirmation(out) {
		fmt.Println("Aborted.")
		return
	}

	fmt.Println("\nSummary:")
	fmt.Printf("Input: %s\nOutput: %s\nBlock size: %d\nCount: %d\nSkip: %d\nSeek: %d\nCompression: %s\nDecompression: %s\nProgress: %v\nVerify: %v\nChecksum: %s\n",
		in, out, bs, cnt, skip, seek, compress, decompress, progress, verify, checksum)
	if !promptConfirm("Proceed with these settings?", true) {
		fmt.Println("Aborted.")
		return
	}
	doCopy(in, out, bs, cnt, skip, seek, compress, decompress, progress, verify, checksum)
}

func runWipeMenu() {
	clearScreen()
	out := selectDeviceOrFile("Select device/file to wipe", false)
	if out == "Back" {
		return
	}
	mode := promptSelect("Wipe mode", []string{"zero", "random"})
	bs := promptInt("Block size (bytes)", 4096)
	cnt := promptInt("Block count (-1 for all)", -1)
	progress := promptConfirm("Show progress?", true)
	dryRun := promptConfirm("Dry run (show what would be done)?", false)

	// Check for overwrite confirmation
	if !checkOverwriteConfirmation(out) {
		fmt.Println("Aborted.")
		return
	}

	fmt.Printf("\nSummary:\nOutput: %s\nMode: %s\nBlock size: %d\nCount: %d\nProgress: %v\nDry run: %v\n",
		out, mode, bs, cnt, progress, dryRun)
	if !promptConfirm("Proceed with these settings?", true) {
		fmt.Println("Aborted.")
		return
	}
	doWipe(out, mode, bs, cnt, progress, dryRun)
}

func runVerifyMenu() {
	clearScreen()
	in := selectDeviceOrFile("Select input file/device", true)
	if in == "Back" {
		return
	}
	clearScreen()
	out := selectDeviceOrFile("Select output file/device", false)
	if out == "Back" {
		return
	}
	bs := promptInt("Block size (bytes)", 4096)
	fmt.Printf("\nSummary:\nInput: %s\nOutput: %s\nBlock size: %d\n", in, out, bs)
	if !promptConfirm("Proceed with verification?", true) {
		fmt.Println("Aborted.")
		return
	}
	match := compareFiles(in, out, bs)
	if match {
		fmt.Println("Verification: OK")
	} else {
		fmt.Println("Verification: FAIL")
		os.Exit(2)
	}
}

func runDeviceInfoMenu() {
	clearScreen()
	dev := selectDeviceOrFile("Select device/file for info", true)
	if dev == "Back" {
		return
	}
	printDeviceInfo(dev)
	fmt.Print("Press Enter to continue...")
	readInput()
}

func selectDeviceOrFile(label string, allowBack bool) string {
	fmt.Printf("%s:\n", label)
	fmt.Println("1. Type path manually")
	fmt.Println("2. Detect Devices")
	if allowBack {
		fmt.Println("3. Back")
	}
	fmt.Print("Enter choice: ")

	choice := readInput()

	switch choice {
	case "1":
		return promptInput("Enter file/device path")
	case "2":
		devices, err := detectDevices()
		if err != nil {
			fmt.Printf("Error detecting devices: %v\n", err)
			fmt.Print("Press Enter to continue...")
			readInput()
			return selectDeviceOrFile(label, allowBack)
		}

		clearScreen()
		fmt.Println("Available devices:")
		for i, device := range devices {
			fmt.Printf("%d. %s (%s) - %s\n", i+1, device.Path, humanizeBytes(uint64(device.Size)), device.Model)
		}
		if allowBack {
			fmt.Printf("%d. Back\n", len(devices)+1)
		}

		fmt.Print("Enter choice: ")
		deviceChoice := readInput()
		idx, err := strconv.Atoi(deviceChoice)
		if err != nil || idx < 1 {
			fmt.Println("Invalid choice")
			fmt.Print("Press Enter to continue...")
			readInput()
			return selectDeviceOrFile(label, allowBack)
		}

		if allowBack && idx == len(devices)+1 {
			return "Back"
		}

		if idx > len(devices) {
			fmt.Println("Invalid choice")
			fmt.Print("Press Enter to continue...")
			readInput()
			return selectDeviceOrFile(label, allowBack)
		}

		device := devices[idx-1]
		printDeviceInfo(device.Path)
		return device.Path
	case "3":
		if allowBack {
			return "Back"
		}
		fallthrough
	default:
		fmt.Println("Invalid choice")
		fmt.Print("Press Enter to continue...")
		readInput()
		return selectDeviceOrFile(label, allowBack)
	}
}

func printDeviceInfo(devicePath string) {
	fmt.Printf("\nDevice Info for %s:\n", devicePath)

	// Try to get device info from our detection
	devices, err := detectDevices()
	if err == nil {
		for _, device := range devices {
			if device.Path == devicePath {
				if isJSONMode() {
					writeJSON(device)
				} else {
					fmt.Printf("  Path: %s\n", device.Path)
					fmt.Printf("  Name: %s\n", device.Name)
					fmt.Printf("  Size: %s\n", humanizeBytes(uint64(device.Size)))
					fmt.Printf("  Block Size: %d\n", device.BlockSize)
					fmt.Printf("  Filesystem: %s\n", device.Filesystem)
					fmt.Printf("  Mountpoint: %s\n", device.Mountpoint)
					fmt.Printf("  Model: %s\n", device.Model)
					fmt.Printf("  Vendor: %s\n", device.Vendor)
					fmt.Printf("  Removable: %v\n", device.Removable)
					fmt.Printf("  Read-only: %v\n", device.ReadOnly)
					fmt.Printf("  Rotational: %v\n", device.Rotational)
				}
				return
			}
		}
	}

	// Fallback: try as file
	if stat, err := os.Stat(devicePath); err == nil {
		fmt.Printf("  File size: %s\n", humanizeBytes(uint64(stat.Size())))
	} else {
		fmt.Printf("  Error accessing device: %v\n", err)
	}
}

// ----------- Enhanced Menu Functions -----------

// Enhanced device/file selector with better UI
func selectDeviceOrFileEnhanced(title string, allowBack bool) (string, error) {
	// Clear screen and draw header once at the start
	clearScreen()
	drawHeader()

	menu := newMenu(title, 10, 5, 60)

	menu.addItem("Type path manually", "Enter a file or device path manually", func() error {
		path := showInputDialog("Enter Path", "Enter file/device path:", "/dev/")
		if path != "" {
			return fmt.Errorf("selected:%s", path)
		}
		return nil
	})

	menu.addItem("Detect Devices", "Automatically detect available storage devices", func() error {
		devices, err := detectDevices()
		if err != nil {
			drawStatusBar("Error detecting devices: "+err.Error(), "error")
			readKey()
			return nil
		}

		if len(devices) == 0 {
			drawStatusBar("No devices found", "warning")
			readKey()
			return nil
		}

		// Create device selection menu - don't clear screen here, let the menu handle it
		deviceMenu := newMenu("Select Device", 5, 3, 70)

		for _, device := range devices {
			desc := fmt.Sprintf("%s - %s (%s)", device.Path, humanizeBytes(uint64(device.Size)), device.Model)
			// Capture device path in closure properly
			devicePath := device.Path
			deviceMenu.addItem(device.Name, desc, func() error {
				return fmt.Errorf("selected:%s", devicePath)
			})
		}

		if allowBack {
			deviceMenu.addItem("Back", "Return to previous menu", func() error {
				return nil
			})
		}

		// Clear screen before running the device menu
		clearScreen()
		drawHeader()
		return deviceMenu.run()
	})

	if allowBack {
		menu.addItem("Back", "Return to previous menu", func() error {
			return fmt.Errorf("back")
		})
	}

	err := menu.run()
	if err != nil {
		if strings.HasPrefix(err.Error(), "selected:") {
			return strings.TrimPrefix(err.Error(), "selected:"), nil
		}
		if err.Error() == "back" {
			return "Back", nil
		}
	}

	return "", err
}

// Enhanced copy menu
func runEnhancedCopyMenu() error {
	// Input selection
	input, err := selectDeviceOrFileEnhanced("Select Input File/Device", true)
	if err != nil || input == "Back" {
		return err
	}

	// Output selection
	output, err := selectDeviceOrFileEnhanced("Select Output File/Device", true)
	if err != nil || output == "Back" {
		return err
	}

	// Configuration menu
	clearScreen()
	configMenu := newMenu("Copy Configuration", 5, 3, 70)

	// Default values
	blockSize := 4096
	blockCount := -1
	skipBlocks := int64(0)
	seekBlocks := int64(0)
	enableSparse := false
	enableProgress := true
	enableVerify := false
	compressionLevel := "none"
	checksumAlg := "none"

	configMenu.addItem("Block Size", fmt.Sprintf("Current: %d bytes", blockSize), func() error {
		input := showInputDialog("Block Size", "Enter block size in bytes:", fmt.Sprintf("%d", blockSize))
		if val, err := strconv.Atoi(input); err == nil && val > 0 {
			blockSize = val
		}
		return nil
	})

	configMenu.addItem("Block Count", fmt.Sprintf("Current: %d (-1 = all)", blockCount), func() error {
		input := showInputDialog("Block Count", "Enter number of blocks to copy (-1 for all):", fmt.Sprintf("%d", blockCount))
		if val, err := strconv.Atoi(input); err == nil {
			blockCount = val
		}
		return nil
	})

	configMenu.addItem("Sparse Files", fmt.Sprintf("Current: %v", enableSparse), func() error {
		enableSparse = showConfirmDialog("Sparse Files", "Enable sparse file optimization?", enableSparse)
		return nil
	})

	configMenu.addItem("Progress Display", fmt.Sprintf("Current: %v", enableProgress), func() error {
		enableProgress = showConfirmDialog("Progress Display", "Show progress during copy?", enableProgress)
		return nil
	})

	configMenu.addItem("Verification", fmt.Sprintf("Current: %v", enableVerify), func() error {
		enableVerify = showConfirmDialog("Verification", "Verify data after copy?", enableVerify)
		return nil
	})

	configMenu.addItem("Start Copy", "Begin the copy operation with current settings", func() error {
		// Show summary and confirm
		summary := fmt.Sprintf("Input: %s\nOutput: %s\nBlock Size: %d\nBlock Count: %d\nSparse: %v\nProgress: %v\nVerify: %v",
			input, output, blockSize, blockCount, enableSparse, enableProgress, enableVerify)

		if showConfirmDialog("Confirm Copy", summary+"\n\nProceed with copy?", true) {
			// Check overwrite confirmation
			if !checkOverwriteConfirmation(output) {
				drawStatusBar("Copy cancelled by user", "info")
				return nil
			}

			// Perform the copy
			doCopy(input, output, blockSize, blockCount, skipBlocks, seekBlocks,
				compressionLevel, "auto", enableProgress, enableVerify, checksumAlg)

			drawStatusBar("Copy completed successfully", "success")
			readKey()
		}
		return nil
	})

	configMenu.addItem("Back", "Return to main menu", func() error {
		return fmt.Errorf("back")
	})

	return configMenu.run()
}

// Enhanced wipe menu
func runEnhancedWipeMenu() error {
	// Device selection
	device, err := selectDeviceOrFileEnhanced("Select Device/File to Wipe", true)
	if err != nil || device == "Back" {
		return err
	}

	// Configuration
	clearScreen()
	wipeMenu := newMenu("Wipe Configuration", 5, 3, 70)

	wipeMode := "zero"
	blockSize := 4096
	blockCount := -1
	enableProgress := true
	dryRun := false

	wipeMenu.addItem("Wipe Mode", fmt.Sprintf("Current: %s", wipeMode), func() error {
		clearScreen()
		modeMenu := newMenu("Select Wipe Mode", 10, 8, 50)
		modeMenu.addItem("Zero", "Fill with zeros (faster)", func() error {
			wipeMode = "zero"
			return fmt.Errorf("selected")
		})
		modeMenu.addItem("Random", "Fill with random data (more secure)", func() error {
			wipeMode = "random"
			return fmt.Errorf("selected")
		})
		modeMenu.run()
		return nil
	})

	wipeMenu.addItem("Block Size", fmt.Sprintf("Current: %d bytes", blockSize), func() error {
		input := showInputDialog("Block Size", "Enter block size in bytes:", fmt.Sprintf("%d", blockSize))
		if val, err := strconv.Atoi(input); err == nil && val > 0 {
			blockSize = val
		}
		return nil
	})

	wipeMenu.addItem("Dry Run", fmt.Sprintf("Current: %v", dryRun), func() error {
		dryRun = showConfirmDialog("Dry Run", "Show what would be done without actually wiping?", dryRun)
		return nil
	})

	wipeMenu.addItem("Start Wipe", "Begin the wipe operation", func() error {
		summary := fmt.Sprintf("Device: %s\nMode: %s\nBlock Size: %d\nDry Run: %v",
			device, wipeMode, blockSize, dryRun)

		if showConfirmDialog("Confirm Wipe", summary+"\n\nThis will DESTROY all data!\nProceed?", false) {
			doWipe(device, wipeMode, blockSize, blockCount, enableProgress, dryRun)
			drawStatusBar("Wipe completed", "success")
			readKey()
		}
		return nil
	})

	wipeMenu.addItem("Back", "Return to main menu", func() error {
		return fmt.Errorf("back")
	})

	return wipeMenu.run()
}

// Enhanced verify menu
func runEnhancedVerifyMenu() error {
	// Input selection
	input, err := selectDeviceOrFileEnhanced("Select First File/Device", true)
	if err != nil || input == "Back" {
		return err
	}

	// Output selection
	output, err := selectDeviceOrFileEnhanced("Select Second File/Device", true)
	if err != nil || output == "Back" {
		return err
	}

	// Configuration
	blockSize := 4096

	summary := fmt.Sprintf("First: %s\nSecond: %s\nBlock Size: %d",
		input, output, blockSize)

	if showConfirmDialog("Confirm Verification", summary+"\n\nProceed with verification?", true) {
		match := compareFiles(input, output, blockSize)
		if match {
			drawStatusBar("Verification: Files match", "success")
		} else {
			drawStatusBar("Verification: Files do NOT match", "error")
		}
		readKey()
	}

	return nil
}

// Enhanced device info menu
func runEnhancedDeviceInfoMenu() error {
	device, err := selectDeviceOrFileEnhanced("Select Device/File for Info", true)
	if err != nil || device == "Back" {
		return err
	}

	clearScreen()
	printDeviceInfo(device)
	drawStatusBar("Press any key to continue...", "info")
	readKey()

	return nil
}

// ----------- Utility Functions -----------

func humanizeBytes(b uint64) string {
	const unit = 1024
	if b < unit {
		return fmt.Sprintf("%d B", b)
	}
	div, exp := unit, 0
	for n := b / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(b)/float64(div), "KMGTPE"[exp])
}

// ----------- Throttle Controller -----------

func newThrottleController(bytesPerSecond int64) *ThrottleController {
	return &ThrottleController{
		bytesPerSecond: bytesPerSecond,
		lastTime:       time.Now(),
	}
}

func (tc *ThrottleController) throttle(bytesTransferred int64) {
	if tc.bytesPerSecond <= 0 {
		return // No throttling
	}

	tc.mu.Lock()
	defer tc.mu.Unlock()

	now := time.Now()
	elapsed := now.Sub(tc.lastTime)

	if elapsed >= time.Second {
		// Reset for new second
		tc.lastTime = now
		tc.bytesThisSecond = bytesTransferred
		return
	}

	tc.bytesThisSecond += bytesTransferred

	if tc.bytesThisSecond >= tc.bytesPerSecond {
		// Need to sleep until next second
		sleepTime := time.Second - elapsed
		time.Sleep(sleepTime)
		tc.lastTime = time.Now()
		tc.bytesThisSecond = 0
	}
}

// ----------- Hash Functions -----------

func createHasher(algorithm string) hash.Hash {
	switch strings.ToLower(algorithm) {
	case "md5":
		return md5.New()
	case "sha256":
		return sha256.New()
	case "sha512":
		return sha512.New()
	default:
		return nil
	}
}

func createHashers(algorithms string) map[string]hash.Hash {
	hashers := make(map[string]hash.Hash)
	if algorithms == "" || algorithms == "none" {
		return hashers
	}

	algList := strings.Split(algorithms, ",")
	for _, alg := range algList {
		alg = strings.TrimSpace(alg)
		if hasher := createHasher(alg); hasher != nil {
			hashers[alg] = hasher
		}
	}
	return hashers
}

func calculateFileHash(path string, algorithm string) (string, error) {
	if algorithm == "" || algorithm == "none" {
		return "", nil
	}

	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hasher := createHasher(algorithm)
	if hasher == nil {
		return "", fmt.Errorf("unsupported hash algorithm: %s", algorithm)
	}

	_, err = io.Copy(hasher, file)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", hasher.Sum(nil)), nil
}

// ----------- Progress Reporting -----------

// ProgressReporter handles consistent progress reporting across all operations
type ProgressReporter struct {
	startTime      time.Time
	lastUpdate     time.Time
	lastBytes      int64
	operation      string
	jsonMode       bool
	terminalWidth  int
	lastLineLength int
}

// NewProgressReporter creates a new progress reporter
func NewProgressReporter(operation string, jsonMode bool) *ProgressReporter {
	return &ProgressReporter{
		startTime:     time.Now(),
		lastUpdate:    time.Now(),
		operation:     operation,
		jsonMode:      jsonMode,
		terminalWidth: getTerminalWidth(),
	}
}

// getTerminalWidth returns the terminal width, defaulting to 80 if unable to detect
func getTerminalWidth() int {
	// Try to get terminal width from environment or default to 80
	return 80 // Simple default for now
}

// Update reports progress with consistent formatting and calculations
func (pr *ProgressReporter) Update(bytesDone, bytesTotal int64, blocksDone int) {
	now := time.Now()
	elapsed := now.Sub(pr.startTime).Seconds()

	// Calculate speeds
	var currentSpeed, avgSpeed float64
	if elapsed > 0 {
		avgSpeed = float64(bytesDone) / elapsed / 1024 / 1024
	}

	// Calculate current speed based on recent progress
	timeSinceLastUpdate := now.Sub(pr.lastUpdate).Seconds()
	if timeSinceLastUpdate > 0 {
		bytesSinceLastUpdate := bytesDone - pr.lastBytes
		currentSpeed = float64(bytesSinceLastUpdate) / timeSinceLastUpdate / 1024 / 1024
	}

	// Calculate percentage and ETA
	var percentDone, eta float64
	if bytesTotal > 0 {
		percentDone = float64(bytesDone) * 100.0 / float64(bytesTotal)
		if currentSpeed > 0 {
			remaining := float64(bytesTotal - bytesDone)
			eta = remaining / (currentSpeed * 1024 * 1024)
		}
	}

	// Create progress info
	info := ProgressInfo{
		BytesDone:   bytesDone,
		BytesTotal:  bytesTotal,
		BlocksDone:  blocksDone,
		Speed:       currentSpeed,
		AvgSpeed:    avgSpeed,
		ElapsedTime: elapsed,
		ETA:         eta,
		PercentDone: percentDone,
		Operation:   pr.operation,
	}

	// Output JSON if in JSON mode
	if pr.jsonMode {
		writeJSON(info)
	}

	// Always show progress on terminal
	pr.printTerminalProgress(info)

	// Update tracking variables
	pr.lastUpdate = now
	pr.lastBytes = bytesDone
}

// printTerminalProgress displays progress on the terminal with enhanced formatting and colors
func (pr *ProgressReporter) printTerminalProgress(info ProgressInfo) {
	// Create simple, reliable progress bar
	barWidth := 30
	filled := int(info.PercentDone * float64(barWidth) / 100.0)
	if filled > barWidth {
		filled = barWidth
	}
	if filled < 0 {
		filled = 0
	}

	// Create progress bar with simple characters for reliability
	progressBar := "[" + strings.Repeat("=", filled) + strings.Repeat("-", barWidth-filled) + "]"

	// Format ETA
	eta := ""
	if info.ETA > 0 && info.ETA < 86400 { // Less than 24 hours
		etaDuration := time.Duration(info.ETA * float64(time.Second)).Round(time.Second)
		eta = fmt.Sprintf(" ETA: %s", etaDuration)
	}

	// Build simple progress line without colors for now
	progressLine := fmt.Sprintf("\r%s %.1f%% %s/%s %.2f MB/s%s",
		progressBar,
		info.PercentDone,
		humanizeBytes(uint64(info.BytesDone)),
		humanizeBytes(uint64(info.BytesTotal)),
		info.Speed,
		eta)

	// Ensure we don't exceed terminal width
	if len(progressLine) > 120 {
		progressLine = progressLine[:117] + "..."
	}

	// Print the progress line and clear to end of line
	fmt.Fprintf(os.Stderr, "%s\033[K", progressLine)
}

// Calculate display length without ANSI escape sequences
func (pr *ProgressReporter) calculateDisplayLength(text string) int {
	// Simple approximation - remove common ANSI sequences
	// In production, you'd use a proper ANSI parser
	length := len(text)

	// Rough estimate: subtract common ANSI sequence lengths
	ansiCount := strings.Count(text, "\033[")
	if ansiCount > 0 {
		length -= ansiCount * 8 // Average ANSI sequence length
	}

	return length
}

// createProgressBar creates a visual progress bar (legacy)
func (pr *ProgressReporter) createProgressBar(width int, percent float64) string {
	if percent > 100 {
		percent = 100
	}
	if percent < 0 {
		percent = 0
	}

	filled := int(percent * float64(width) / 100)
	if filled > width {
		filled = width
	}

	bar := strings.Repeat("█", filled) + strings.Repeat("░", width-filled)
	return fmt.Sprintf("[%s] %.1f%%", bar, percent)
}

// createEnhancedProgressBar creates a colorized progress bar with better visual appeal
func (pr *ProgressReporter) createEnhancedProgressBar(width int, percent float64) string {
	if percent > 100 {
		percent = 100
	}
	if percent < 0 {
		percent = 0
	}

	filled := int(percent * float64(width) / 100)

	// Choose colors based on progress
	var barColor string
	if percent < 25 {
		barColor = ColorBrightRed
	} else if percent < 50 {
		barColor = ColorBrightYellow
	} else if percent < 75 {
		barColor = ColorBrightBlue
	} else {
		barColor = ThemeProgress
	}

	// Create the bar with colors
	filledBar := barColor + strings.Repeat("█", filled) + ColorReset
	emptyBar := ThemeDisabled + strings.Repeat("░", width-filled) + ColorReset

	// Format percentage with color
	var percentColor string
	if percent == 100 {
		percentColor = ThemeSuccess
	} else if percent > 75 {
		percentColor = ThemeHighlight
	} else {
		percentColor = ThemeNormal
	}

	return fmt.Sprintf("[%s%s] %s%.1f%%%s",
		filledBar, emptyBar,
		percentColor, percent, ColorReset)
}

// Finish completes the progress reporting with a final update and summary
func (pr *ProgressReporter) Finish(bytesDone, bytesTotal int64, blocksDone int) {
	// Final update
	pr.Update(bytesDone, bytesTotal, blocksDone)

	// Add newline to move cursor to next line
	fmt.Fprintf(os.Stderr, "\n")

	// Calculate operation duration
	duration := time.Since(pr.startTime)

	// Calculate average speed
	avgSpeed := float64(bytesDone) / duration.Seconds() / (1024 * 1024) // MB/s

	// Print operation summary
	fmt.Fprintf(os.Stderr, "\nOperation Summary:\n")
	fmt.Fprintf(os.Stderr, "  Bytes copied: %s\n", humanizeBytes(uint64(bytesDone)))
	if bytesTotal > 0 {
		fmt.Fprintf(os.Stderr, "  Total size: %s\n", humanizeBytes(uint64(bytesTotal)))
		fmt.Fprintf(os.Stderr, "  Completion: %.1f%%\n", float64(bytesDone)*100.0/float64(bytesTotal))
	}
	fmt.Fprintf(os.Stderr, "  Blocks copied: %d\n", blocksDone)
	fmt.Fprintf(os.Stderr, "  Duration: %s\n", duration.Round(time.Second))
	fmt.Fprintf(os.Stderr, "  Average speed: %.2f MB/s\n", avgSpeed)
	fmt.Fprintf(os.Stderr, "\n")
}

// Legacy function for compatibility - will be removed after refactoring
func printProgress(info ProgressInfo) {
	if isJSONMode() {
		writeJSON(info)
	}

	// Always show progress on terminal (even in JSON mode)
	progressBar := createProgressBar(40, info.PercentDone)
	eta := ""
	if info.ETA > 0 && info.ETA < 86400 { // Less than 24 hours
		eta = fmt.Sprintf(" ETA: %s", time.Duration(info.ETA*float64(time.Second)).Round(time.Second))
	}

	// Use carriage return to overwrite the same line
	fmt.Fprintf(os.Stderr, "\r%s %s/%s (%.2f MB/s)%s",
		progressBar,
		humanizeBytes(uint64(info.BytesDone)),
		humanizeBytes(uint64(info.BytesTotal)),
		info.Speed,
		eta)
}

// Legacy function for compatibility - will be removed after refactoring
func createProgressBar(width int, percent float64) string {
	filled := int(percent * float64(width) / 100)
	bar := strings.Repeat("█", filled) + strings.Repeat("░", width-filled)
	return fmt.Sprintf("[%s] %.1f%%", bar, percent)
}

// ----------- Sparse File Support -----------

// isZeroBlock efficiently checks if a block consists entirely of zero bytes
// Optimized implementation based on dd's is_nul function with SIMD-like optimizations
func isZeroBlock(data []byte) bool {
	length := len(data)
	if length == 0 {
		return true
	}

	// For very small blocks, use simple comparison
	if length <= 8 {
		for _, b := range data {
			if b != 0 {
				return false
			}
		}
		return true
	}

	// Quick check: if first or last byte is non-zero, block is not zero
	// This catches most non-zero blocks very quickly
	if data[0] != 0 || data[length-1] != 0 {
		return false
	}

	// For larger blocks, use word-aligned comparison for better performance
	// Check first few bytes to align to 8-byte boundary
	p := 0
	for p < length && (p&7) != 0 {
		if data[p] != 0 {
			return false
		}
		p++
	}

	// Check 8 bytes at a time using uint64 for maximum efficiency
	// This is similar to dd's approach but optimized for Go
	for p+8 <= length {
		// Use unsafe pointer conversion for better performance
		// Convert 8 bytes to uint64 for faster comparison
		word := uint64(data[p]) | uint64(data[p+1])<<8 | uint64(data[p+2])<<16 | uint64(data[p+3])<<24 |
			uint64(data[p+4])<<32 | uint64(data[p+5])<<40 | uint64(data[p+6])<<48 | uint64(data[p+7])<<56
		if word != 0 {
			return false
		}
		p += 8
	}

	// Check remaining bytes (less than 8)
	for p < length {
		if data[p] != 0 {
			return false
		}
		p++
	}

	return true
}

// ensureFileSize ensures the output file has the correct size after sparse operations
// This is important when the last operation was a seek (hole creation)
// Uses ftruncate to set size without allocating space (preserves sparseness)
func ensureFileSize(outFile *os.File, expectedSize int64) error {
	// Get current file size
	fileInfo, err := outFile.Stat()
	if err != nil {
		return err
	}

	currentSize := fileInfo.Size()
	if currentSize < expectedSize {
		// Use ftruncate to extend file size without allocating space
		// This preserves sparse holes
		err = unix.Ftruncate(int(outFile.Fd()), expectedSize)
		if err != nil {
			return fmt.Errorf("failed to truncate file to size %d: %v", expectedSize, err)
		}
	}

	return nil
}

func writeZeroBlock(writer io.Writer, size int) error {
	// For sparse file support, we could use SEEK_HOLE/SEEK_DATA
	// For now, just write zeros
	zeros := make([]byte, size)
	_, err := writer.Write(zeros)
	return err
}

// SparseRegion represents a data or hole region in a sparse file
type SparseRegion struct {
	Offset int64 // Start offset of the region
	Length int64 // Length of the region
	IsHole bool  // true if this is a hole, false if it contains data
}

// SparseBlockTracker tracks data and hole regions during sequential block reading
type SparseBlockTracker struct {
	blockSize       int
	currentOffset   int64
	currentRegion   *SparseRegion
	regions         []SparseRegion
	zeroBuffer      []byte
	inDataRegion    bool
	dataStartOffset int64
}

// NewSparseBlockTracker creates a new sparse block tracker
func NewSparseBlockTracker(blockSize int) *SparseBlockTracker {
	return &SparseBlockTracker{
		blockSize:  blockSize,
		zeroBuffer: make([]byte, blockSize),
		regions:    make([]SparseRegion, 0),
	}
}

// ProcessBlock processes a block and updates sparse region tracking
func (sbt *SparseBlockTracker) ProcessBlock(data []byte) {
	blockIsZero := isZeroBlock(data)

	if blockIsZero {
		// This is a zero block (potential hole)
		if sbt.inDataRegion {
			// End the current data region
			sbt.regions = append(sbt.regions, SparseRegion{
				Offset: sbt.dataStartOffset,
				Length: sbt.currentOffset - sbt.dataStartOffset,
				IsHole: false,
			})
			sbt.inDataRegion = false
		}
		// Zero blocks are handled implicitly as gaps between data regions
	} else {
		// This is a data block
		if !sbt.inDataRegion {
			// Start a new data region
			sbt.dataStartOffset = sbt.currentOffset
			sbt.inDataRegion = true
		}
		// Continue the current data region
	}

	sbt.currentOffset += int64(len(data))
}

// Finalize completes the sparse region tracking and returns the final regions
func (sbt *SparseBlockTracker) Finalize() []SparseRegion {
	// Close any open data region
	if sbt.inDataRegion {
		sbt.regions = append(sbt.regions, SparseRegion{
			Offset: sbt.dataStartOffset,
			Length: sbt.currentOffset - sbt.dataStartOffset,
			IsHole: false,
		})
	}

	// Fill in holes between data regions
	finalRegions := make([]SparseRegion, 0)
	lastOffset := int64(0)

	for _, dataRegion := range sbt.regions {
		// Add hole before this data region if there's a gap
		if dataRegion.Offset > lastOffset {
			finalRegions = append(finalRegions, SparseRegion{
				Offset: lastOffset,
				Length: dataRegion.Offset - lastOffset,
				IsHole: true,
			})
		}

		// Add the data region
		finalRegions = append(finalRegions, dataRegion)
		lastOffset = dataRegion.Offset + dataRegion.Length
	}

	// Add final hole if file ends with zeros
	if sbt.currentOffset > lastOffset {
		finalRegions = append(finalRegions, SparseRegion{
			Offset: lastOffset,
			Length: sbt.currentOffset - lastOffset,
			IsHole: true,
		})
	}

	return finalRegions
}

// GetDataRegions returns only the data regions (non-holes)
func (sbt *SparseBlockTracker) GetDataRegions() []SparseRegion {
	dataRegions := make([]SparseRegion, 0)
	for _, region := range sbt.regions {
		if !region.IsHole {
			dataRegions = append(dataRegions, region)
		}
	}
	return dataRegions
}

// CopyMode defines the different copy operation modes
type CopyMode int

const (
	CopyModeSingleThreaded CopyMode = iota
	CopyModeMultiThreaded
	CopyModeSequentialSparse
	CopyModeDataOnly
)

// copyUnified is the unified copy function that handles all copy modes
func copyUnified(reader io.Reader, writer io.Writer, outFile *os.File, bs, count int, inputSize int64, progress bool, throttleController *ThrottleController, hasher hash.Hash, result *OperationResult, checkpoint *Checkpoint, checkpointFile string, mode CopyMode) {
	switch mode {
	case CopyModeDataOnly:
		copyWithDataOnlyApproach(reader, writer, outFile, bs, count, inputSize, progress, throttleController, hasher, result, checkpoint, checkpointFile)
	case CopyModeSequentialSparse:
		copyWithSequentialSparseDetection(reader, writer, outFile, bs, count, inputSize, progress, throttleController, hasher, result, checkpoint, checkpointFile)
	case CopyModeMultiThreaded:
		copyMultiThreadedWithCheckpoint(reader, writer, outFile, bs, count, inputSize, progress, throttleController, hasher, result, checkpoint, checkpointFile)
	case CopyModeSingleThreaded:
		copySingleThreadedWithCheckpoint(reader, writer, outFile, bs, count, inputSize, progress, throttleController, hasher, result, checkpoint, checkpointFile)
	default:
		result.Success = false
		result.Error = "Invalid copy mode specified"
	}
}

// copyWithDataOnlyApproach creates a sparse image containing only data blocks
// This is the most aggressive sparse approach - only data-containing blocks are written
func copyWithDataOnlyApproach(reader io.Reader, writer io.Writer, outFile *os.File, bs, count int, inputSize int64, progress bool, throttleController *ThrottleController, hasher hash.Hash, result *OperationResult, checkpoint *Checkpoint, checkpointFile string) {
	buf := make([]byte, bs)
	blocks := checkpoint.BlocksDone        // Start from checkpoint
	var total int64 = checkpoint.BytesDone // Start from checkpoint
	progressInterval := 128
	if bs >= 1024*1024 {
		progressInterval = 8
	}

	// Initialize progress reporter
	var progressReporter *ProgressReporter
	if progress {
		progressReporter = NewProgressReporter("copy", isJSONMode())
	}

	// Checkpoint saving interval
	checkpointInterval := 100
	if bs >= 1024*1024 {
		checkpointInterval = 10
	}

	// Initialize sparse block tracker
	sparseTracker := NewSparseBlockTracker(bs)

	// Skip to checkpoint position if resuming
	if checkpoint.BytesDone > 0 {
		sparseTracker.currentOffset = checkpoint.BytesDone
	}

	// Track statistics
	dataBlocks := 0
	zeroBlocks := 0

	for {
		if count >= 0 && blocks >= count {
			break
		}

		n, err := reader.Read(buf)
		if n > 0 {
			data := buf[:n]

			// Process block for sparse detection
			sparseTracker.ProcessBlock(data)

			// Only write non-zero blocks, create holes for zero blocks
			if !isZeroBlock(data) {
				// This is a data block - write it
				wn, werr := writer.Write(data)
				if werr != nil || wn != n {
					result.Success = false
					result.Error = fmt.Sprintf("Write error at block %d: %v", blocks, werr)
					return
				}
				dataBlocks++
			} else {
				// This is a zero block - create a hole instead of writing
				if err := createSparseHole(outFile, writer, int64(n)); err != nil {
					// If hole creation fails, write zeros as fallback
					wn, werr := writer.Write(data)
					if werr != nil || wn != n {
						result.Success = false
						result.Error = fmt.Sprintf("Fallback write error at block %d: %v", blocks, werr)
						return
					}
				}
				zeroBlocks++
			}

			// Update hash (include zeros for sparse blocks)
			if hasher != nil {
				hasher.Write(data)
			}

			total += int64(n)
			blocks++

			// Update checkpoint
			checkpoint.BytesDone = total
			checkpoint.BlocksDone = blocks

			// Throttling
			if throttleController != nil {
				throttleController.throttle(int64(n))
			}

			// Progress reporting
			if progressReporter != nil && blocks%progressInterval == 0 {
				progressReporter.Update(total, inputSize, blocks)
			}

			// Save checkpoint periodically
			if blocks%checkpointInterval == 0 {
				if err := saveCheckpoint(checkpointFile, checkpoint); err != nil {
					// Log error but don't fail the operation
					fmt.Fprintf(os.Stderr, "Warning: Failed to save checkpoint: %v\n", err)
				}
			}
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			result.Success = false
			result.Error = fmt.Sprintf("Read error at block %d: %v", blocks, err)
			return
		}
	}

	// Finalize sparse region detection
	detectedRegions := sparseTracker.Finalize()

	// Log sparse optimization results
	if zeroBlocks > 0 {
		spacesSaved := float64(zeroBlocks) / float64(blocks) * 100
		fmt.Printf("Sparse optimization: %d data blocks, %d zero blocks (%.1f%% space saved)\n",
			dataBlocks, zeroBlocks, spacesSaved)

		if len(detectedRegions) > 0 {
			fmt.Printf("Detected %d sparse regions\n", len(detectedRegions))
		}
	}

	// Ensure file has correct size for sparse files
	if inputSize > 0 {
		if err := ensureFileSize(outFile, inputSize); err != nil {
			result.Success = false
			result.Error = fmt.Sprintf("Failed to ensure correct file size: %v", err)
			return
		}
	}

	// Final progress update
	if progressReporter != nil {
		progressReporter.Finish(total, inputSize, blocks)
	}

	result.BytesCopied = total
	result.BlocksCopied = blocks
}

// detectSparseRegions uses SEEK_HOLE and SEEK_DATA to map out sparse regions in a file
func detectSparseRegions(file *os.File) ([]SparseRegion, error) {
	var regions []SparseRegion

	// Get file size
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, err
	}
	fileSize := fileInfo.Size()

	if fileSize == 0 {
		return regions, nil
	}

	offset := int64(0)

	for offset < fileSize {
		// Find next data region using unix package
		dataStart, err := unix.Seek(int(file.Fd()), offset, unix.SEEK_DATA)
		if err != nil {
			// No more data regions, rest is a hole
			if offset < fileSize {
				regions = append(regions, SparseRegion{
					Offset: offset,
					Length: fileSize - offset,
					IsHole: true,
				})
			}
			break
		}

		// If there's a gap between current offset and data start, it's a hole
		if dataStart > offset {
			regions = append(regions, SparseRegion{
				Offset: offset,
				Length: dataStart - offset,
				IsHole: true,
			})
		}

		// Find end of data region (start of next hole)
		holeStart, err := unix.Seek(int(file.Fd()), dataStart, unix.SEEK_HOLE)
		if err != nil {
			// Data extends to end of file
			regions = append(regions, SparseRegion{
				Offset: dataStart,
				Length: fileSize - dataStart,
				IsHole: false,
			})
			break
		}

		// Add data region
		regions = append(regions, SparseRegion{
			Offset: dataStart,
			Length: holeStart - dataStart,
			IsHole: false,
		})

		offset = holeStart
	}

	return regions, nil
}

// isSparseFile checks if a file has any holes using SEEK_HOLE
func isSparseFile(file *os.File) bool {

	fileInfo, err := file.Stat()
	if err != nil {
		return false
	}

	if fileInfo.Size() == 0 {
		return false
	}

	// Check if there's a hole at the beginning
	holeStart, err := unix.Seek(int(file.Fd()), 0, unix.SEEK_HOLE)
	if err != nil {
		return false
	}

	// If hole starts at offset 0 and file size > 0, or hole starts before end of file
	return holeStart < fileInfo.Size()
}

// createSparseHole efficiently creates a hole in the output file
// Uses the most reliable method for creating sparse holes
func createSparseHole(outFile *os.File, writer io.Writer, size int64) error {
	// In sparse mode, writer should be the file directly (not buffered)
	// But check anyway and flush if needed
	if bw, ok := writer.(*bufio.Writer); ok {
		if err := bw.Flush(); err != nil {
			return fmt.Errorf("failed to flush buffer before creating hole: %v", err)
		}
	}

	// Get current position
	currentPos, err := outFile.Seek(0, io.SeekCurrent)
	if err != nil {
		return fmt.Errorf("failed to get current position: %v", err)
	}

	// Method 1: Try using fallocate with FALLOC_FL_PUNCH_HOLE to create a hole

	// First, ensure the file is large enough by seeking to the end position
	endPos := currentPos + size
	if _, err := outFile.Seek(endPos, io.SeekStart); err != nil {
		return fmt.Errorf("failed to seek to end position: %v", err)
	}

	// Try to punch a hole in the region we want to be sparse
	err = unix.Fallocate(int(outFile.Fd()), unix.FALLOC_FL_PUNCH_HOLE|unix.FALLOC_FL_KEEP_SIZE, currentPos, size)

	if err != nil {
		// Fallback: extend file first, then seek to create hole
		// Write a single byte at the end position to ensure the file extends
		if _, err := outFile.Write([]byte{0}); err != nil {
			return fmt.Errorf("failed to extend file: %v", err)
		}
		// Seek back to end position (the write advanced us by 1 byte)
		if _, err := outFile.Seek(endPos, io.SeekStart); err != nil {
			return fmt.Errorf("failed to create hole by seeking: %v", err)
		}
	} else {
		// Hole punching succeeded, position at end
		if _, err := outFile.Seek(endPos, io.SeekStart); err != nil {
			return fmt.Errorf("failed to seek after hole creation: %v", err)
		}
	}

	// For buffered writers (shouldn't happen in sparse mode), reset their position
	if bw, ok := writer.(*bufio.Writer); ok {
		bw.Reset(outFile)
	}

	return nil
}

// optimizedSparseWrite writes data efficiently, creating holes for zero blocks
// Based on dd's sparse writing approach with improved error handling
func optimizedSparseWrite(data []byte, outFile *os.File, writer io.Writer, enableSparse bool) (int, error) {
	if !enableSparse || !isZeroBlock(data) {
		// Normal write for non-zero data or when sparse is disabled
		return writer.Write(data)
	}

	// Create hole instead of writing zeros using lseek (similar to dd's approach)
	size := int64(len(data))
	currentPos, err := outFile.Seek(0, io.SeekCurrent)
	if err != nil {
		// Fall back to writing zeros if seek fails
		return writer.Write(data)
	}

	// Seek forward to create hole
	_, err = outFile.Seek(currentPos+size, io.SeekStart)
	if err != nil {
		// Fall back to writing zeros if hole creation fails
		return writer.Write(data)
	}

	return len(data), nil
}

// preallocateSparseFile preallocates space for a sparse file if supported
func preallocateSparseFile(file *os.File, size int64) error {
	// Use fallocate syscall to preallocate space efficiently
	// This is optional - if it fails, the file will still work

	// Try to preallocate space (this may fail on some filesystems)
	err := unix.Fallocate(int(file.Fd()), unix.FALLOC_FL_KEEP_SIZE, 0, size)

	if err != nil {
		// Preallocation failed, but this is not critical
		// The file will still work, just potentially less efficiently
		return err
	}

	return nil
}

// calculateSparseProgress calculates accurate progress for sparse files
func calculateSparseProgress(regions []SparseRegion, currentOffset int64) (dataProcessed int64, totalData int64) {
	for _, region := range regions {
		if !region.IsHole {
			// Count data regions toward total
			totalData += region.Length

			// Count processed data
			if currentOffset >= region.Offset+region.Length {
				// Entire region processed
				dataProcessed += region.Length
			} else if currentOffset > region.Offset {
				// Partially processed region
				dataProcessed += currentOffset - region.Offset
			}
		}
	}
	return dataProcessed, totalData
}

// getSparseFileDataSize returns the total amount of actual data (non-hole) in a sparse file
func getSparseFileDataSize(file *os.File) (int64, error) {
	regions, err := detectSparseRegions(file)
	if err != nil {
		// Fall back to file size if sparse detection fails
		info, err := file.Stat()
		if err != nil {
			return 0, err
		}
		return info.Size(), nil
	}

	var dataSize int64
	for _, region := range regions {
		if !region.IsHole {
			dataSize += region.Length
		}
	}

	return dataSize, nil
}

// ----------- Resume Functions -----------

// doResumeWithError returns an error instead of calling os.Exit
func doResumeWithError(checkpointFile string) error {
	checkpoint, err := loadCheckpoint(checkpointFile)
	if err != nil {
		return fmt.Errorf("failed to load checkpoint: %v", err)
	}

	fmt.Printf("Resuming copy operation from checkpoint:\n")
	fmt.Printf("  Input: %s\n", checkpoint.Input)
	fmt.Printf("  Output: %s\n", checkpoint.Output)
	fmt.Printf("  Progress: %s / %s (%.1f%%)\n",
		humanizeBytes(uint64(checkpoint.BytesDone)),
		humanizeBytes(uint64(checkpoint.BytesDone+1000000)), // Approximate, will be recalculated
		float64(checkpoint.BlocksDone)*100.0/float64(checkpoint.Count))

	// Resume the copy operation with checkpoint state
	return doCopyWithCheckpointAndError(checkpoint, checkpointFile, true, false, checkpoint.Checksum)
}

// Legacy doResume function for backward compatibility
func doResume(checkpointFile string) {
	if err := doResumeWithError(checkpointFile); err != nil {
		fmt.Fprintf(os.Stderr, "Resume operation failed: %v\n", err)
		os.Exit(1)
	}
}

// loadCheckpoint loads a checkpoint from a JSON file
func loadCheckpoint(filename string) (*Checkpoint, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read checkpoint file: %v", err)
	}

	var checkpoint Checkpoint
	if err := json.Unmarshal(data, &checkpoint); err != nil {
		return nil, fmt.Errorf("failed to parse checkpoint file: %v", err)
	}

	// Validate checkpoint
	if checkpoint.Input == "" || checkpoint.Output == "" {
		return nil, fmt.Errorf("invalid checkpoint: missing input or output")
	}

	if checkpoint.BlockSize <= 0 {
		return nil, fmt.Errorf("invalid checkpoint: invalid block size")
	}

	return &checkpoint, nil
}

// saveCheckpoint saves the current state to a checkpoint file
func saveCheckpoint(filename string, checkpoint *Checkpoint) error {
	checkpoint.LastUpdate = time.Now().Unix()

	data, err := json.MarshalIndent(checkpoint, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal checkpoint: %v", err)
	}

	// Write to temporary file first, then rename for atomicity
	tempFile := filename + ".tmp"
	if err := os.WriteFile(tempFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write checkpoint file: %v", err)
	}

	if err := os.Rename(tempFile, filename); err != nil {
		os.Remove(tempFile) // Clean up temp file
		return fmt.Errorf("failed to rename checkpoint file: %v", err)
	}

	return nil
}

// generateCheckpointFilename generates a default checkpoint filename
func generateCheckpointFilename(input, output string) string {
	// Create a simple hash of input+output for uniqueness
	hash := fmt.Sprintf("%x", md5.Sum([]byte(input+output)))
	return fmt.Sprintf("ken_checkpoint_%s.json", hash[:8])
}

// ----------- Core Copy Function -----------

// doCopyWithError returns an error instead of calling os.Exit
func doCopyWithError(input, output string, bs, count int, skip, seek int64, compress, decompress string, progress bool, verify bool, checksumAlg string) error {
	// Handle partition-only mode
	if *partitionOnly {
		// Initialize hasher if needed
		var hasher hash.Hash
		if checksumAlg != "" && checksumAlg != "none" {
			hasher = createHasher(checksumAlg)
		}

		// Initialize throttle controller
		var throttleController *ThrottleController
		if *throttle > 0 {
			throttleController = newThrottleController(*throttle)
		}

		// Create result structure
		result := &OperationResult{
			Success:   true,
			Operation: "partition-copy",
			Input:     input,
			Output:    output,
			Checksums: make(map[string]string),
		}

		// Perform partition-only copy
		err := copyPartitionOnly(input, output, bs, progress, throttleController, hasher, result)
		if err != nil {
			return err
		}

		// Handle checksum verification
		if hasher != nil {
			checksum := fmt.Sprintf("%x", hasher.Sum(nil))
			fmt.Printf("Checksum (%s): %s\n", checksumAlg, checksum)

			if *jsonOutput != "" {
				result.Checksums[checksumAlg] = checksum
			}
		}

		// Write JSON output if requested
		if *jsonOutput != "" {
			outputResult(*result)
		}

		return nil
	}

	// Handle skipempty mode
	if *skipEmpty && count == -1 {
		// Only apply skipempty if count is not already specified
		if newCount, err := calculateSkipEmptyCount(input, bs); err == nil {
			count = newCount
			fmt.Printf("Skip-empty mode enabled: limiting copy to %d blocks\n", count)
		} else {
			fmt.Fprintf(os.Stderr, "Warning: Skip-empty mode failed: %v\n", err)
			fmt.Fprintf(os.Stderr, "Continuing with full copy...\n")
		}
	}

	// Create checkpoint for this operation
	checkpoint := &Checkpoint{
		Input:      input,
		Output:     output,
		BlockSize:  bs,
		Count:      count,
		Skip:       skip,
		Seek:       seek,
		BytesDone:  0,
		BlocksDone: 0,
		Compress:   compress,
		Decompress: decompress,
		Sparse:     *sparse,
		Checksum:   checksumAlg,
		StartTime:  time.Now().Unix(),
		LastUpdate: time.Now().Unix(),
	}

	checkpointFile := generateCheckpointFilename(input, output)
	return doCopyWithCheckpointAndError(checkpoint, checkpointFile, progress, verify, checksumAlg)
}

// Legacy doCopy function for backward compatibility
func doCopy(input, output string, bs, count int, skip, seek int64, compress, decompress string, progress bool, verify bool, checksumAlg string) {
	if err := doCopyWithError(input, output, bs, count, skip, seek, compress, decompress, progress, verify, checksumAlg); err != nil {
		fmt.Fprintf(os.Stderr, "Copy operation failed: %v\n", err)
		os.Exit(1)
	}
}

// doCopyWithCheckpointAndError returns an error instead of calling os.Exit
func doCopyWithCheckpointAndError(checkpoint *Checkpoint, checkpointFile string, progress bool, verify bool, checksumAlg string) error {
	input := checkpoint.Input
	output := checkpoint.Output
	bs := checkpoint.BlockSize
	count := checkpoint.Count
	skip := checkpoint.Skip
	seek := checkpoint.Seek
	compress := checkpoint.Compress
	decompress := checkpoint.Decompress

	if *dryRun {
		fmt.Printf("Dry run: would copy %s to %s (bs=%d, count=%d, skip=%d, seek=%d)\n",
			input, output, bs, count, skip, seek)
		return nil
	}

	// Check for overwrite confirmation (only in interactive mode or when not appending)
	if len(os.Args) == 1 || !*appendMode {
		if !checkOverwriteConfirmation(output) {
			return fmt.Errorf("operation cancelled by user")
		}
	}

	start := time.Now()
	result := OperationResult{
		Operation: "copy",
		Input:     input,
		Output:    output,
		Success:   true,
		Checksums: make(map[string]string),
	}

	// Open input file
	in, err := os.Open(input)
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("Failed to open input: %v", err)
		outputResult(result)
		return fmt.Errorf("failed to open input file %s: %v", input, err)
	}
	defer in.Close()

	// Get input file size for progress tracking
	var inputSize int64
	if stat, err := in.Stat(); err == nil {
		if stat.Mode()&os.ModeDevice != 0 || strings.HasPrefix(input, "/dev/") {
			// For device files, try to get the actual device size
			inputSize = getDeviceSize(input)
		} else {
			inputSize = stat.Size()
		}

		// Adjust for skip
		if skip > 0 {
			inputSize -= int64(skip) * int64(bs)
			if inputSize < 0 {
				inputSize = 0
			}
		}
		// Adjust for count limit
		if count > 0 {
			maxSize := int64(count) * int64(bs)
			if inputSize > maxSize {
				inputSize = maxSize
			}
		}
	}

	// Open output file
	var openFlags int = os.O_CREATE | os.O_WRONLY
	if *appendMode {
		openFlags |= os.O_APPEND
	} else {
		openFlags |= os.O_TRUNC
	}

	out, err := os.OpenFile(output, openFlags, 0666)
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("Failed to open output: %v", err)
		outputResult(result)
		return fmt.Errorf("failed to open output file %s: %v", output, err)
	}
	defer out.Close()

	// Preallocate space for sparse files if enabled and we know the input size
	if *sparse && inputSize > 0 && !*appendMode {
		preallocateSparseFile(out, inputSize)
		// Ignore errors - preallocation is optional
	}

	// Handle skip and seek
	if skip > 0 {
		_, err := in.Seek(int64(skip)*int64(bs), io.SeekStart)
		if err != nil {
			result.Success = false
			result.Error = fmt.Sprintf("Failed to skip: %v", err)
			outputResult(result)
			return fmt.Errorf("failed to skip %d blocks in input: %v", skip, err)
		}
		inputSize -= int64(skip) * int64(bs)
	}
	if seek > 0 && !*appendMode {
		_, err := out.Seek(int64(seek)*int64(bs), io.SeekStart)
		if err != nil {
			result.Success = false
			result.Error = fmt.Sprintf("Failed to seek: %v", err)
			outputResult(result)
			return fmt.Errorf("failed to seek %d blocks in output: %v", seek, err)
		}
	}

	// Setup readers and writers with compression/decompression
	var reader io.Reader = bufio.NewReaderSize(in, bs*4)
	var writer io.Writer

	// Don't use buffered writer for sparse mode as it interferes with seeking
	if *sparse && (decompress == "" || decompress == "none") && (compress == "" || compress == "none") {
		writer = out
	} else {
		writer = bufio.NewWriterSize(out, bs*4)
	}

	// Decompression
	if decompress != "" && decompress != "none" {
		if decompress == "auto" {
			ext := strings.ToLower(filepath.Ext(input))
			if ext == ".gz" {
				decompress = "gzip"
			}
		}
		if decompress == "gzip" {
			gr, err := gzip.NewReader(reader)
			if err != nil {
				result.Success = false
				result.Error = fmt.Sprintf("Failed to create gzip reader: %v", err)
				outputResult(result)
				return fmt.Errorf("failed to create gzip reader for input: %v", err)
			}
			defer gr.Close()
			reader = gr
		}
	}

	// Compression
	if compress != "" && compress != "none" {
		if strings.HasPrefix(compress, "gzip") {
			// Parse compression level (default 6)
			level := gzip.DefaultCompression
			if len(compress) > 4 {
				if levelStr := compress[4:]; levelStr != "" {
					if parsedLevel, err := strconv.Atoi(levelStr); err == nil && parsedLevel >= 1 && parsedLevel <= 9 {
						level = parsedLevel
					}
				}
			}

			gw, err := gzip.NewWriterLevel(writer, level)
			if err != nil {
				result.Success = false
				result.Error = fmt.Sprintf("Failed to create gzip writer: %v", err)
				outputResult(result)
				return fmt.Errorf("failed to create gzip writer for output: %v", err)
			}
			defer gw.Close()
			writer = gw
		}
	}

	// Setup throttling
	var throttleController *ThrottleController
	if *throttle > 0 {
		throttleController = newThrottleController(*throttle)
	}

	// Setup hashing
	var hasher hash.Hash
	if checksumAlg != "" && checksumAlg != "none" {
		hasher = createHasher(checksumAlg)
	}

	// Choose copy method based on sparse flag and compression settings
	useSparseAware := *sparse &&
		(decompress == "" || decompress == "none") &&
		(compress == "" || compress == "none")

	// Determine copy mode
	var copyMode CopyMode
	if *sparseOnly {
		copyMode = CopyModeDataOnly
	} else if useSparseAware {
		copyMode = CopyModeSequentialSparse
	} else if *threads > 1 {
		copyMode = CopyModeMultiThreaded
	} else {
		copyMode = CopyModeSingleThreaded
	}

	// Use unified copy function
	copyUnified(reader, writer, out, bs, count, inputSize, progress, throttleController, hasher, &result, checkpoint, checkpointFile, copyMode)

	// Flush buffers
	if bw, ok := writer.(*bufio.Writer); ok {
		bw.Flush()
	}

	// Calculate final results
	result.ElapsedTime = time.Since(start).Seconds()
	if result.ElapsedTime > 0 {
		result.AvgSpeed = float64(result.BytesCopied) / result.ElapsedTime / 1024 / 1024
	}

	// Add checksum if calculated
	if hasher != nil {
		result.Checksums[checksumAlg] = fmt.Sprintf("%x", hasher.Sum(nil))
	}

	// Verification - automatically disabled when compression is used
	if verify {
		if compress != "" && compress != "none" {
			// Skip verification when compression is used since compressed output
			// cannot be directly compared to uncompressed input
			if !isJSONMode() {
				fmt.Fprintf(os.Stderr, "\nNote: Verification automatically disabled when compression is used\n")
			}
			// Leave result.Verified as nil to indicate verification was skipped
			// (not performed due to compression)
		} else {
			verified := compareFiles(input, output, bs)
			result.Verified = &verified
			if !verified {
				result.Success = false
				result.Error = "Verification failed"
			}
		}
	}

	// Fix file permissions if running as root
	if os.Getuid() == 0 && !strings.HasPrefix(output, "/dev/") {
		fixFilePermissions(output)
	}

	// Clean up checkpoint file on successful completion
	if result.Success {
		os.Remove(checkpointFile)
	}

	outputResult(result)

	if !result.Success {
		return fmt.Errorf("copy operation failed: %s", result.Error)
	}
	return nil
}

// Legacy doCopyWithCheckpoint function for backward compatibility
func doCopyWithCheckpoint(checkpoint *Checkpoint, checkpointFile string, progress bool, verify bool, checksumAlg string) {
	if err := doCopyWithCheckpointAndError(checkpoint, checkpointFile, progress, verify, checksumAlg); err != nil {
		fmt.Fprintf(os.Stderr, "Copy operation failed: %v\n", err)
		os.Exit(1)
	}
}

// Fix file permissions when running as root
func fixFilePermissions(filePath string) {
	// Get the original user from SUDO_UID and SUDO_GID environment variables
	if sudoUID := os.Getenv("SUDO_UID"); sudoUID != "" {
		if sudoGID := os.Getenv("SUDO_GID"); sudoGID != "" {
			if uid, err := strconv.Atoi(sudoUID); err == nil {
				if gid, err := strconv.Atoi(sudoGID); err == nil {
					os.Chown(filePath, uid, gid)
				}
			}
		}
	}
}

// verifyFilesystemIntegrity performs basic filesystem verification on the output
func verifyFilesystemIntegrity(outputPath string) error {
	// For device files, try to detect partitions and check filesystems
	if strings.HasPrefix(outputPath, "/dev/") {
		return verifyDeviceFilesystems(outputPath)
	}

	// For image files, try to mount and verify
	if strings.HasSuffix(outputPath, ".img") {
		return verifyImageFilesystems(outputPath)
	}

	return nil
}

// verifyDeviceFilesystems checks filesystems on a device using native partition parsing
func verifyDeviceFilesystems(devicePath string) error {
	// Try to detect partitions using native parsing
	partitionTable, err := parsePartitionTable(devicePath)
	if err != nil {
		// Not a partitioned device or can't parse
		return nil
	}

	// Check each partition's filesystem
	for _, region := range partitionTable.Regions {
		// Skip header and backup regions, only check actual partitions
		if region.Type != "header" && region.Type != "backup" {
			if err := checkFilesystemHealth(region.Name); err != nil {
				return fmt.Errorf("filesystem check failed for %s: %v", region.Name, err)
			}
		}
	}

	return nil
}

// verifyImageFilesystems checks filesystems in an image file using loop device
func verifyImageFilesystems(imagePath string) error {
	// This is more complex and would require loop device setup
	// For now, just check if the file is readable and has reasonable size
	stat, err := os.Stat(imagePath)
	if err != nil {
		return fmt.Errorf("cannot access image file: %v", err)
	}

	if stat.Size() == 0 {
		return fmt.Errorf("image file is empty")
	}

	// Try to read the first few bytes to ensure it's accessible
	file, err := os.Open(imagePath)
	if err != nil {
		return fmt.Errorf("cannot open image file: %v", err)
	}
	defer file.Close()

	buffer := make([]byte, 512)
	_, err = file.Read(buffer)
	if err != nil && err != io.EOF {
		return fmt.Errorf("cannot read from image file: %v", err)
	}

	return nil
}

// checkFilesystemHealth performs a read-only filesystem check
func checkFilesystemHealth(devicePath string) error {
	// Try to get filesystem type first
	cmd := exec.Command("blkid", "-o", "value", "-s", "TYPE", devicePath)
	output, err := cmd.Output()
	if err != nil {
		// No filesystem detected or blkid not available
		return nil
	}

	fsType := strings.TrimSpace(string(output))
	if fsType == "" {
		return nil
	}

	// Perform read-only check based on filesystem type
	switch fsType {
	case "ext2", "ext3", "ext4":
		cmd = exec.Command("e2fsck", "-n", devicePath)
	case "xfs":
		cmd = exec.Command("xfs_check", devicePath)
	case "btrfs":
		cmd = exec.Command("btrfs", "check", "--readonly", devicePath)
	default:
		// Unknown filesystem type, skip check
		return nil
	}

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("filesystem check failed for %s (%s): %v", devicePath, fsType, err)
	}

	return nil
}

// Single-threaded copy implementation with checkpoint support
func copySingleThreadedWithCheckpoint(reader io.Reader, writer io.Writer, outFile *os.File, bs, count int, inputSize int64, progress bool, throttleController *ThrottleController, hasher hash.Hash, result *OperationResult, checkpoint *Checkpoint, checkpointFile string) {
	buf := make([]byte, bs)
	blocks := checkpoint.BlocksDone        // Start from checkpoint
	var total int64 = checkpoint.BytesDone // Start from checkpoint
	progressInterval := 128
	if bs >= 1024*1024 {
		progressInterval = 8
	}

	// Initialize progress reporter
	var progressReporter *ProgressReporter
	if progress {
		progressReporter = NewProgressReporter("copy", isJSONMode())
	}

	// Initialize bad sector tracker
	var badSectorTracker *BadSectorTracker
	if *skipBadSectors {
		badSectorTracker = NewBadSectorTracker()
	}

	// Checkpoint saving interval (every 100 blocks or 10MB, whichever is smaller)
	checkpointInterval := 100
	if bs >= 1024*1024 {
		checkpointInterval = 10
	}

	for {
		if count >= 0 && blocks >= count {
			break
		}

		n, err := reader.Read(buf)
		if n > 0 {
			data := buf[:n]

			// Use optimized sparse write
			wn, werr := optimizedSparseWrite(data, outFile, writer, *sparse)
			if werr != nil || wn != n {
				result.Success = false
				result.Error = fmt.Sprintf("Write error at block %d: %v", blocks, werr)
				return
			}

			// Update hash if enabled
			if hasher != nil {
				hasher.Write(data)
			}

			total += int64(n)
			blocks++

			// Update checkpoint
			checkpoint.BytesDone = total
			checkpoint.BlocksDone = blocks

			// Throttling
			if throttleController != nil {
				throttleController.throttle(int64(n))
			}

			// Progress reporting
			if progressReporter != nil && blocks%progressInterval == 0 {
				progressReporter.Update(total, inputSize, blocks)
			}

			// Save checkpoint periodically
			if blocks%checkpointInterval == 0 {
				if err := saveCheckpoint(checkpointFile, checkpoint); err != nil {
					// Log error but don't fail the operation
					fmt.Fprintf(os.Stderr, "Warning: Failed to save checkpoint: %v\n", err)
				}
			}
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			// Try to handle bad sector if skip-bad-sectors is enabled
			if zeroBuffer, shouldSkip := handleReadError(err, int64(blocks), total, bs, badSectorTracker); shouldSkip {
				// Write zero buffer instead of the bad sector
				wn, werr := optimizedSparseWrite(zeroBuffer, outFile, writer, *sparse)
				if werr != nil || wn != bs {
					result.Success = false
					result.Error = fmt.Sprintf("Write error at block %d (bad sector replacement): %v", blocks, werr)
					return
				}

				// Update hash with zero buffer
				if hasher != nil {
					hasher.Write(zeroBuffer)
				}

				total += int64(bs)
				blocks++

				// Update checkpoint
				checkpoint.BytesDone = total
				checkpoint.BlocksDone = blocks

				// Continue with next block
				continue
			} else {
				// Normal error handling - fail the operation
				result.Success = false
				result.Error = fmt.Sprintf("Read error at block %d: %v", blocks, err)
				return
			}
		}
	}

	// Final progress update
	if progressReporter != nil {
		progressReporter.Finish(total, inputSize, blocks)
	}

	result.BytesCopied = total
	result.BlocksCopied = blocks

	// Add bad sector information to result if applicable
	if badSectorTracker != nil {
		result.BadSectors = badSectorTracker.GetBadSectors()
		result.BadSectorCount = badSectorTracker.GetTotalBadSectors()
	}

	// Perform post-copy filesystem verification if output is a device or image
	outputPath := checkpoint.Output
	if strings.HasPrefix(outputPath, "/dev/") || strings.HasSuffix(outputPath, ".img") {
		if err := verifyFilesystemIntegrity(outputPath); err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Filesystem verification failed: %v\n", err)
			// Don't fail the operation, just warn
		}
	}
}

// DataBlock represents a block of data with metadata for multi-threaded processing
type DataBlock struct {
	Data     []byte
	BlockNum int
	Size     int
	IsLast   bool
	Error    error
}

// Multi-threaded copy implementation with checkpoint support
func copyMultiThreadedWithCheckpoint(reader io.Reader, writer io.Writer, outFile *os.File, bs, count int, inputSize int64, progress bool, throttleController *ThrottleController, hasher hash.Hash, result *OperationResult, checkpoint *Checkpoint, checkpointFile string) {
	numWorkers := *threads
	if numWorkers < 1 {
		numWorkers = 1
	}
	if numWorkers == 1 {
		// Fall back to single-threaded for efficiency
		copySingleThreadedWithCheckpoint(reader, writer, outFile, bs, count, inputSize, progress, throttleController, hasher, result, checkpoint, checkpointFile)
		return
	}

	// Create channels for the pipeline
	readChan := make(chan DataBlock, numWorkers*2)  // Buffer for read operations
	writeChan := make(chan DataBlock, numWorkers*2) // Buffer for write operations
	doneChan := make(chan bool, 1)
	errorChan := make(chan error, numWorkers+2)

	// Shared state with proper synchronization
	var (
		totalBytes  int64 = checkpoint.BytesDone  // Start from checkpoint
		totalBlocks int   = checkpoint.BlocksDone // Start from checkpoint
		stateMutex  sync.Mutex
		wg          sync.WaitGroup
	)

	// Initialize bad sector tracker
	var badSectorTracker *BadSectorTracker
	if *skipBadSectors {
		badSectorTracker = NewBadSectorTracker()
	}

	// Initialize progress reporter
	var progressReporter *ProgressReporter
	if progress {
		progressReporter = NewProgressReporter("copy", isJSONMode())
	}

	// Progress update interval
	progressInterval := 128
	if bs >= 1024*1024 {
		progressInterval = 8
	}

	// Checkpoint saving interval
	checkpointInterval := 100
	if bs >= 1024*1024 {
		checkpointInterval = 10
	}

	// Reader goroutine - reads data from input
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer close(readChan)

		buf := make([]byte, bs)
		blockNum := checkpoint.BlocksDone // Start from checkpoint

		for {
			if count >= 0 && blockNum >= count {
				break
			}

			n, err := reader.Read(buf)
			if n > 0 {
				// Create a copy of the data for this block
				blockData := make([]byte, n)
				copy(blockData, buf[:n])

				block := DataBlock{
					Data:     blockData,
					BlockNum: blockNum,
					Size:     n,
					IsLast:   false,
					Error:    nil,
				}

				select {
				case readChan <- block:
					blockNum++
				case <-doneChan:
					return
				}
			}

			if err == io.EOF {
				// Send final block to indicate completion
				select {
				case readChan <- DataBlock{IsLast: true, BlockNum: blockNum}:
				case <-doneChan:
				}
				break
			}
			if err != nil {
				// Try to handle bad sector if skip-bad-sectors is enabled
				if zeroBuffer, shouldSkip := handleReadError(err, int64(blockNum), int64(blockNum)*int64(bs), bs, badSectorTracker); shouldSkip {
					// Send zero-filled block instead of the bad sector
					block := DataBlock{
						Data:     zeroBuffer,
						BlockNum: blockNum,
						Size:     bs,
						IsLast:   false,
						Error:    nil,
					}

					select {
					case readChan <- block:
						blockNum++
					case <-doneChan:
						return
					}
					continue // Continue reading next block
				} else {
					// If not skipping bad sectors, send error and terminate
					select {
					case errorChan <- fmt.Errorf("read error at block %d: %v", blockNum, err):
					case <-doneChan:
					}
					return
				}
			}
		}
	}()

	// Hash processor goroutine - processes blocks for hashing and forwards to write
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer close(writeChan)

		for block := range readChan {
			if block.IsLast {
				// Forward the last block marker
				select {
				case writeChan <- block:
				case <-doneChan:
				}
				break
			}

			if block.Error != nil {
				select {
				case errorChan <- block.Error:
				case <-doneChan:
				}
				return
			}

			// Update hash if enabled
			if hasher != nil {
				hasher.Write(block.Data)
			}

			// Forward to write channel
			select {
			case writeChan <- block:
			case <-doneChan:
				return
			}
		}
	}()

	// Writer goroutine - writes data to output
	wg.Add(1)
	go func() {
		defer wg.Done()

		for block := range writeChan {
			if block.IsLast {
				break
			}

			if block.Error != nil {
				select {
				case errorChan <- block.Error:
				case <-doneChan:
				}
				return
			}

			// Write the data
			wn, werr := optimizedSparseWrite(block.Data, outFile, writer, *sparse)
			if werr != nil || wn != block.Size {
				select {
				case errorChan <- fmt.Errorf("write error at block %d: %v", block.BlockNum, werr):
				case <-doneChan:
				}
				return
			}

			// Update shared state
			stateMutex.Lock()
			totalBytes += int64(block.Size)
			totalBlocks++
			currentBytes := totalBytes
			currentBlocks := totalBlocks

			// Update checkpoint
			checkpoint.BytesDone = currentBytes
			checkpoint.BlocksDone = currentBlocks
			stateMutex.Unlock()

			// Throttling
			if throttleController != nil {
				throttleController.throttle(int64(block.Size))
			}

			// Progress reporting
			if progressReporter != nil && currentBlocks%progressInterval == 0 {
				progressReporter.Update(currentBytes, inputSize, currentBlocks)
			}

			// Save checkpoint periodically
			if currentBlocks%checkpointInterval == 0 {
				if err := saveCheckpoint(checkpointFile, checkpoint); err != nil {
					// Log error but don't fail the operation
					fmt.Fprintf(os.Stderr, "Warning: Failed to save checkpoint: %v\n", err)
				}
			}
		}
	}()

	// Wait for completion or error
	go func() {
		wg.Wait()
		close(doneChan)
	}()

	// Wait for completion or handle errors
	select {
	case err := <-errorChan:
		close(doneChan)
		result.Success = false
		result.Error = err.Error()
		return
	case <-doneChan:
		// All goroutines completed successfully
	}

	// Final progress update
	if progressReporter != nil {
		progressReporter.Finish(totalBytes, inputSize, totalBlocks)
	}

	result.BytesCopied = totalBytes
	result.BlocksCopied = totalBlocks

	// Add bad sector information to result if applicable
	if badSectorTracker != nil {
		result.BadSectors = badSectorTracker.GetBadSectors()
		result.BadSectorCount = badSectorTracker.GetTotalBadSectors()
	}

	// Perform post-copy filesystem verification if output is a device or image
	outputPath := checkpoint.Output
	if strings.HasPrefix(outputPath, "/dev/") || strings.HasSuffix(outputPath, ".img") {
		if err := verifyFilesystemIntegrity(outputPath); err != nil {
			fmt.Fprintf(os.Stderr, "Warning: Filesystem verification failed: %v\n", err)
			// Don't fail the operation, just warn
		}
	}
}

// copyWithSequentialSparseDetection implements the new sequential sparse detection approach
func copyWithSequentialSparseDetection(reader io.Reader, writer io.Writer, outFile *os.File, bs, count int, inputSize int64, progress bool, throttleController *ThrottleController, hasher hash.Hash, result *OperationResult, checkpoint *Checkpoint, checkpointFile string) {
	buf := make([]byte, bs)
	blocks := checkpoint.BlocksDone        // Start from checkpoint
	var total int64 = checkpoint.BytesDone // Start from checkpoint
	progressInterval := 128
	if bs >= 1024*1024 {
		progressInterval = 8
	}

	// Initialize progress reporter
	var progressReporter *ProgressReporter
	if progress {
		progressReporter = NewProgressReporter("copy", isJSONMode())
	}

	// Checkpoint saving interval
	checkpointInterval := 100
	if bs >= 1024*1024 {
		checkpointInterval = 10
	}

	// Initialize sparse block tracker
	sparseTracker := NewSparseBlockTracker(bs)

	// Skip to checkpoint position if resuming
	if checkpoint.BytesDone > 0 {
		sparseTracker.currentOffset = checkpoint.BytesDone
	}

	for {
		if count >= 0 && blocks >= count {
			break
		}

		n, err := reader.Read(buf)
		if n > 0 {
			data := buf[:n]

			// Process block for sparse detection
			sparseTracker.ProcessBlock(data)

			// Write using optimized sparse approach
			nwritten, werr := optimizedSparseWrite(data, outFile, writer, true)

			// If sparse hole creation failed or block is not zero, write normally
			if nwritten == 0 {
				wn, werr := writer.Write(data)
				if werr != nil {
					result.Success = false
					result.Error = fmt.Sprintf("Write error at block %d: %v", blocks, werr)
					return
				}
				nwritten = wn
			} else if werr != nil {
				result.Success = false
				result.Error = fmt.Sprintf("Sparse write error at block %d: %v", blocks, werr)
				return
			}

			// Verify we wrote the expected amount
			if nwritten != n {
				result.Success = false
				result.Error = fmt.Sprintf("Incomplete write at block %d: wrote %d, expected %d", blocks, nwritten, n)
				return
			}

			// Update hash (include zeros for sparse blocks)
			if hasher != nil {
				hasher.Write(data)
			}

			total += int64(n)
			blocks++

			// Update checkpoint
			checkpoint.BytesDone = total
			checkpoint.BlocksDone = blocks

			// Throttling
			if throttleController != nil {
				throttleController.throttle(int64(n))
			}

			// Progress reporting
			if progressReporter != nil && blocks%progressInterval == 0 {
				progressReporter.Update(total, inputSize, blocks)
			}

			// Save checkpoint periodically
			if blocks%checkpointInterval == 0 {
				if err := saveCheckpoint(checkpointFile, checkpoint); err != nil {
					// Log error but don't fail the operation
					fmt.Fprintf(os.Stderr, "Warning: Failed to save checkpoint: %v\n", err)
				}
			}
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			result.Success = false
			result.Error = fmt.Sprintf("Read error at block %d: %v", blocks, err)
			return
		}
	}

	// Finalize sparse region detection
	detectedRegions := sparseTracker.Finalize()

	// Log sparse detection results
	if len(detectedRegions) > 0 {
		dataRegions := 0
		holeRegions := 0
		totalDataSize := int64(0)
		totalHoleSize := int64(0)

		for _, region := range detectedRegions {
			if region.IsHole {
				holeRegions++
				totalHoleSize += region.Length
			} else {
				dataRegions++
				totalDataSize += region.Length
			}
		}

		if holeRegions > 0 {
			fmt.Printf("Sparse file detected: %d data regions (%s), %d holes (%s)\n",
				dataRegions, humanizeBytes(uint64(totalDataSize)),
				holeRegions, humanizeBytes(uint64(totalHoleSize)))
		}
	}

	// Ensure file has correct size for sparse files
	if inputSize > 0 {
		if err := ensureFileSize(outFile, inputSize); err != nil {
			result.Success = false
			result.Error = fmt.Sprintf("Failed to ensure correct file size: %v", err)
			return
		}
	}

	// Final progress update
	if progressReporter != nil {
		progressReporter.Finish(total, inputSize, blocks)
	}

	result.BytesCopied = total
	result.BlocksCopied = blocks
}

// Legacy copyWithSparseSupport with checkpoint support (for backward compatibility)
func copyWithSparseSupportWithCheckpoint(reader io.Reader, writer io.Writer, outFile *os.File, bs, count int, inputSize int64, progress bool, throttleController *ThrottleController, hasher hash.Hash, result *OperationResult, checkpoint *Checkpoint, checkpointFile string) {
	buf := make([]byte, bs)
	blocks := checkpoint.BlocksDone        // Start from checkpoint
	var total int64 = checkpoint.BytesDone // Start from checkpoint
	progressInterval := 128
	if bs >= 1024*1024 {
		progressInterval = 8
	}

	// Initialize progress reporter
	var progressReporter *ProgressReporter
	if progress {
		progressReporter = NewProgressReporter("copy", isJSONMode())
	}

	// Checkpoint saving interval
	checkpointInterval := 100
	if bs >= 1024*1024 {
		checkpointInterval = 10
	}

	for {
		if count >= 0 && blocks >= count {
			break
		}

		n, err := reader.Read(buf)
		if n > 0 {
			data := buf[:n]

			// Try to create sparse hole for zero blocks
			nwritten, werr := optimizedSparseWrite(data, outFile, writer, true)

			// If sparse hole creation failed or block is not zero, write normally
			if nwritten == 0 {
				wn, werr := writer.Write(data)
				if werr != nil {
					result.Success = false
					result.Error = fmt.Sprintf("Write error at block %d: %v", blocks, werr)
					return
				}
				nwritten = wn
			} else if werr != nil {
				result.Success = false
				result.Error = fmt.Sprintf("Sparse write error at block %d: %v", blocks, werr)
				return
			}

			// Verify we wrote the expected amount
			if nwritten != n {
				result.Success = false
				result.Error = fmt.Sprintf("Incomplete write at block %d: wrote %d, expected %d", blocks, nwritten, n)
				return
			}

			// Update hash (include zeros for sparse blocks)
			if hasher != nil {
				hasher.Write(data)
			}

			total += int64(n)
			blocks++

			// Update checkpoint
			checkpoint.BytesDone = total
			checkpoint.BlocksDone = blocks

			// Throttling
			if throttleController != nil {
				throttleController.throttle(int64(n))
			}

			// Progress reporting
			if progressReporter != nil && blocks%progressInterval == 0 {
				progressReporter.Update(total, inputSize, blocks)
			}

			// Save checkpoint periodically
			if blocks%checkpointInterval == 0 {
				if err := saveCheckpoint(checkpointFile, checkpoint); err != nil {
					// Log error but don't fail the operation
					fmt.Fprintf(os.Stderr, "Warning: Failed to save checkpoint: %v\n", err)
				}
			}
		}

		if err == io.EOF {
			break
		}
		if err != nil {
			result.Success = false
			result.Error = fmt.Sprintf("Read error at block %d: %v", blocks, err)
			return
		}
	}

	// Final progress update
	if progressReporter != nil {
		progressReporter.Finish(total, inputSize, blocks)
	}

	result.BytesCopied = total
	result.BlocksCopied = blocks
}

// copyWithSparseRegions with checkpoint support
func copyWithSparseRegionsWithCheckpoint(inFile, outFile *os.File, regions []SparseRegion, bs, count int, inputSize int64, progress bool, throttleController *ThrottleController, hasher hash.Hash, result *OperationResult, checkpoint *Checkpoint, checkpointFile string) {
	buf := make([]byte, bs)
	blocks := checkpoint.BlocksDone        // Start from checkpoint
	var total int64 = checkpoint.BytesDone // Start from checkpoint
	progressInterval := 128
	if bs >= 1024*1024 {
		progressInterval = 8
	}

	// Initialize progress reporter
	var progressReporter *ProgressReporter
	if progress {
		progressReporter = NewProgressReporter("copy", isJSONMode())
	}

	// Checkpoint saving interval
	checkpointInterval := 100
	if bs >= 1024*1024 {
		checkpointInterval = 10
	}

	// Calculate starting region based on checkpoint
	startRegionIndex := 0
	remainingSkip := checkpoint.BytesDone
	for i, region := range regions {
		if remainingSkip <= region.Length {
			startRegionIndex = i
			break
		}
		remainingSkip -= region.Length
	}

	for i := startRegionIndex; i < len(regions); i++ {
		region := regions[i]
		if count >= 0 && blocks >= count {
			break
		}

		if region.IsHole {
			// For holes, just seek forward and update hash with zeros
			holeSize := region.Length
			if i == startRegionIndex && remainingSkip > 0 {
				// Adjust for checkpoint position within this region
				holeSize -= remainingSkip
			}

			if err := createSparseHole(outFile, outFile, holeSize); err != nil {
				result.Success = false
				result.Error = fmt.Sprintf("Failed to create hole: %v", err)
				return
			}

			// Update hash with zeros for the hole
			if hasher != nil {
				zeroData := make([]byte, min(holeSize, int64(bs)))
				for j := range zeroData {
					zeroData[j] = 0
				}
				remaining := holeSize
				for remaining > 0 {
					writeSize := min(remaining, int64(len(zeroData)))
					hasher.Write(zeroData[:writeSize])
					remaining -= writeSize
				}
			}

			total += holeSize
			blocks += int(holeSize / int64(bs))

			// Update checkpoint
			checkpoint.BytesDone = total
			checkpoint.BlocksDone = blocks

			// Progress reporting for holes
			if progressReporter != nil {
				progressReporter.Update(total, inputSize, blocks)
			}

			// Save checkpoint periodically
			if blocks%checkpointInterval == 0 {
				if err := saveCheckpoint(checkpointFile, checkpoint); err != nil {
					fmt.Fprintf(os.Stderr, "Warning: Failed to save checkpoint: %v\n", err)
				}
			}
		} else {
			// Data region - seek to position and copy data
			seekPos := region.Offset
			if i == startRegionIndex && remainingSkip > 0 {
				seekPos += remainingSkip
			}

			if _, err := inFile.Seek(seekPos, io.SeekStart); err != nil {
				result.Success = false
				result.Error = fmt.Sprintf("Failed to seek to data region: %v", err)
				return
			}

			remaining := region.Length
			if i == startRegionIndex && remainingSkip > 0 {
				remaining -= remainingSkip
			}

			for remaining > 0 && (count < 0 || blocks < count) {
				readSize := min(remaining, int64(bs))
				n, err := inFile.Read(buf[:readSize])
				if n > 0 {
					data := buf[:n]
					if _, werr := outFile.Write(data); werr != nil {
						result.Success = false
						result.Error = fmt.Sprintf("Write error in data region: %v", werr)
						return
					}

					// Update hash
					if hasher != nil {
						hasher.Write(data)
					}

					total += int64(n)
					blocks++
					remaining -= int64(n)

					// Update checkpoint
					checkpoint.BytesDone = total
					checkpoint.BlocksDone = blocks

					// Throttling
					if throttleController != nil {
						throttleController.throttle(int64(n))
					}

					// Progress reporting
					if progressReporter != nil && blocks%progressInterval == 0 {
						progressReporter.Update(total, inputSize, blocks)
					}

					// Save checkpoint periodically
					if blocks%checkpointInterval == 0 {
						if err := saveCheckpoint(checkpointFile, checkpoint); err != nil {
							fmt.Fprintf(os.Stderr, "Warning: Failed to save checkpoint: %v\n", err)
						}
					}
				}

				if err == io.EOF {
					break
				}
				if err != nil {
					result.Success = false
					result.Error = fmt.Sprintf("Read error in data region: %v", err)
					return
				}
			}
		}
	}

	// Final progress update
	if progressReporter != nil {
		progressReporter.Finish(total, inputSize, blocks)
	}

	result.BytesCopied = total
	result.BlocksCopied = blocks
}

// min returns the minimum of two int64 values
func min(a, b int64) int64 {
	if a < b {
		return a
	}
	return b
}

// Output result in JSON or text format
func outputResult(result OperationResult) {
	if isJSONMode() {
		writeJSON(result)
	}

	// Always show errors on terminal
	if !result.Success {
		fmt.Fprintf(os.Stderr, "Operation failed: %s\n", result.Error)
	}
}

// ----------- Wipe Function -----------

// doWipeWithError returns an error instead of calling os.Exit
func doWipeWithError(path, mode string, bs, count int, progress, dryRun bool) error {
	if dryRun {
		result := OperationResult{
			Operation: "wipe",
			Output:    path,
			Success:   true,
		}
		if isJSONMode() {
			writeJSON(result)
		} else {
			fmt.Printf("Dry run: would wipe %s with %s (bs=%d, count=%d)\n", path, mode, bs, count)
		}
		return nil
	}

	// Check for overwrite confirmation (only in interactive mode)
	if len(os.Args) == 1 {
		if !checkOverwriteConfirmation(path) {
			return fmt.Errorf("operation cancelled by user")
		}
	}

	start := time.Now()
	result := OperationResult{
		Operation: "wipe",
		Output:    path,
		Success:   true,
	}

	out, err := os.OpenFile(path, os.O_CREATE|os.O_WRONLY, 0666)
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("Failed to open output: %v", err)
		outputResult(result)
		return fmt.Errorf("failed to open output file %s: %v", path, err)
	}
	defer out.Close()

	buf := make([]byte, bs)
	blocks := 0
	var total int64

	// Setup throttling
	var throttleController *ThrottleController
	if *throttle > 0 {
		throttleController = newThrottleController(*throttle)
	}

	// Initialize progress reporter
	var progressReporter *ProgressReporter
	if progress {
		progressReporter = NewProgressReporter("wipe", isJSONMode())
	}

	// Calculate total size for progress if count is specified
	var totalSize int64
	if count > 0 {
		totalSize = int64(count) * int64(bs)
	}

	for {
		if count >= 0 && blocks >= count {
			break
		}

		switch strings.ToLower(mode) {
		case "zero":
			for i := range buf {
				buf[i] = 0
			}
		case "random":
			_, err := rand.Read(buf)
			if err != nil {
				result.Success = false
				result.Error = fmt.Sprintf("Failed to generate random data: %v", err)
				outputResult(result)
				return fmt.Errorf("failed to generate random data: %v", err)
			}
		default:
			result.Success = false
			result.Error = fmt.Sprintf("Unknown wipe mode: %s", mode)
			outputResult(result)
			return fmt.Errorf("unknown wipe mode: %s", mode)
		}

		wn, werr := out.Write(buf)
		if werr != nil || wn != len(buf) {
			result.Success = false
			result.Error = fmt.Sprintf("Write error at block %d: %v", blocks, werr)
			outputResult(result)
			return fmt.Errorf("write error at block %d: %v", blocks, werr)
		}

		total += int64(wn)
		blocks++

		// Throttling
		if throttleController != nil {
			throttleController.throttle(int64(wn))
		}

		// Progress reporting
		if progressReporter != nil && blocks%128 == 0 {
			progressReporter.Update(total, totalSize, blocks)
		}
	}

	result.ElapsedTime = time.Since(start).Seconds()
	result.BytesCopied = total
	result.BlocksCopied = blocks
	if result.ElapsedTime > 0 {
		result.AvgSpeed = float64(total) / result.ElapsedTime / 1024 / 1024
	}

	// Final progress update
	if progressReporter != nil {
		progressReporter.Finish(total, totalSize, blocks)
	}

	outputResult(result)
	return nil
}

// Legacy doWipe function for backward compatibility
func doWipe(path, mode string, bs, count int, progress, dryRun bool) {
	if err := doWipeWithError(path, mode, bs, count, progress, dryRun); err != nil {
		fmt.Fprintf(os.Stderr, "Wipe operation failed: %v\n", err)
		os.Exit(1)
	}
}

// ----------- Compare Files Function -----------

func compareFiles(p1, p2 string, bs int) bool {
	f1, err := os.Open(p1)
	if err != nil {
		return false
	}
	defer f1.Close()

	f2, err := os.Open(p2)
	if err != nil {
		return false
	}
	defer f2.Close()

	b1 := make([]byte, bs)
	b2 := make([]byte, bs)

	for {
		n1, e1 := f1.Read(b1)
		n2, e2 := f2.Read(b2)

		if n1 != n2 || !bytes.Equal(b1[:n1], b2[:n2]) {
			return false
		}

		if e1 == io.EOF && e2 == io.EOF {
			break
		}

		if e1 != nil && e1 != io.EOF {
			return false
		}
		if e2 != nil && e2 != io.EOF {
			return false
		}
	}

	return true
}

// ----------- Examples Function -----------

func printExamples() {
	examples := `
Bella Usage Examples:

Basic Operations:
- Copy a file:
  bella -input source.img -output backup.img -bs 4M -progress

- Clone a disk to another disk:
  bella -input /dev/sda -output /dev/sdb -bs 64K -progress

- Create a compressed image of a device:
  bella -input /dev/sda -output disk.img.gz -compress gzip6 -bs 4M -progress

- Create a highly compressed image (slower):
  bella -input /dev/sda -output disk.img.gz -compress gzip9 -bs 4M -progress

- Create a fast compressed image:
  bella -input /dev/sda -output disk.img.gz -compress gzip1 -bs 4M -progress

- Restore a compressed image to a device:
  bella -input disk.img.gz -output /dev/sda -decompress gzip -bs 4M -progress

Advanced Features:
- Copy with sparse file support (preserves holes):
  bella -input sparse.img -output backup.img -sparse -bs 4M -progress

- Copy sparse file with verification:
  bella -input sparse.img -output backup.img -sparse -verify -progress

- Copy sparse file with checksum:
  bella -input sparse.img -output backup.img -sparse -checksum sha256 -progress

- Create aggressive sparse image (only data blocks):
  bella -input /dev/sda -output sparse.img -sparse-only -bs 4M -progress

- Copy with bandwidth throttling (10 MB/s):
  bella -input /dev/sda -output backup.img -throttle 10485760 -progress

- Multi-threaded copy:
  bella -input /dev/sda -output backup.img -threads 4 -bs 1M -progress

- Copy with data integrity checking:
  bella -input source.img -output backup.img -checksum sha256 -verify -progress

- Append to existing file:
  bella -input new_data.log -output existing.log -append -progress

- JSON output for scripting:
  bella -input source.img -output backup.img -json results.json -progress

Wipe Operations:
- Wipe a device with zeros:
  bella -output /dev/sda -wipe zero -bs 4M -progress

- Wipe with random data:
  bella -output /dev/sda -wipe random -bs 4M -progress

- Dry run (show what would be done):
  bella -input source.img -output backup.img -dry-run

Sparse File Operations:
- Copy sparse file efficiently (preserves holes and saves space):
  bella -input sparse.img -output backup.img -sparse -progress

- Create sparse backup of VM disk (can save 50%+ space):
  bella -input /dev/vg0/vm-disk -output vm-backup.img -sparse -bs 4M -progress

- Copy sparse file with compression (disables sparse optimization):
  bella -input sparse.img -output backup.img.gz -sparse -compress gzip6 -progress

- Verify sparse file integrity:
  bella -input sparse.img -output backup.img -sparse -verify -progress

- Copy with checksum verification:
  bella -input sparse.img -output backup.img -sparse -checksum sha256 -progress

New Features:
- Skip empty space (copy only up to last partition + buffer):
  bella -input /dev/sda -output backup.img -skipempty -bs 4M -progress

- Partition-only backup (copy only actual partition data regions):
  bella -input /dev/sda -output backup.img -partition-only -bs 4M -progress

- Memory-based checkpointing (reduces disk writes):
  bella -input /dev/sda -output backup.img -checkpoint-memory -progress

- JSON memory buffering (write to file on completion):
  bella -input source.img -output backup.img -json results.json -json-memory -progress

- Custom temp directory for checkpoint/JSON files:
  bella -input /dev/sda -output backup.img -tempdir /custom/temp -progress

- Combine skip-empty with sparse for maximum efficiency:
  bella -input /dev/sda -output backup.img -skipempty -sparse -bs 4M -progress

Resume Operations:
- Resume interrupted copy:
  bella -resume checkpoint.json

- Resume with different progress settings:
  bella -resume checkpoint.json -progress

Interactive Mode:
- Start interactive mode:
  bella

- Interactive mode with examples:
  bella -examples

Advanced Sparse Features:
- Automatic sparse region detection using SEEK_HOLE/SEEK_DATA
- Optimized region-based copying that skips holes entirely
- Reliable hole creation using fallocate with graceful fallbacks
- Progress calculation based on actual data (excludes holes)
- Block-by-block zero detection for non-regular files
- Note: Compression disables sparse optimization (falls back to regular copy)

Verification:
- Verify two files match:
  ken -input source.img -output backup.img -verify

- Calculate file checksums:
  ken -input file.img -checksum sha512

Block Size Guidelines:
- For SSDs: 1M-4M block size
- For HDDs: 64K-1M block size
- For network storage: 4K-64K block size
- For USB devices: 4K-256K block size
`

	if isJSONMode() {
		result := map[string]string{"examples": examples}
		writeJSON(result)
	} else {
		fmt.Print(examples)
	}
}

// ============================================================================
// NEW TVIEW-BASED UI IMPLEMENTATION
// ============================================================================

// TViewUI represents the new tview-based user interface
type TViewUI struct {
	app        *tview.Application
	pages      *tview.Pages
	mainMenu   *tview.List
	statusBar  *tview.TextView
	currentDir string
}

// NewTViewUI creates a new tview-based UI
func NewTViewUI() *TViewUI {
	ui := &TViewUI{
		app:        tview.NewApplication(),
		pages:      tview.NewPages(),
		mainMenu:   tview.NewList(),
		statusBar:  tview.NewTextView(),
		currentDir: "",
	}

	ui.setupUI()
	return ui
}

// setupUI initializes the UI layout and components
func (ui *TViewUI) setupUI() {
	// Configure main menu
	ui.mainMenu.SetBorder(true).
		SetTitle(" Bella - Advanced Data Copier ").
		SetTitleAlign(tview.AlignCenter)

	// Add main menu items
	ui.mainMenu.AddItem("Copy", "Copy data between files and devices with advanced options", 'c', ui.showCopyMenu)
	ui.mainMenu.AddItem("Wipe", "Securely wipe devices or files with zero or random data", 'w', ui.showWipeMenu)
	ui.mainMenu.AddItem("Verify", "Compare two files or devices for data integrity", 'v', ui.showVerifyMenu)
	ui.mainMenu.AddItem("Device Info", "Display detailed information about storage devices", 'd', ui.showDeviceInfoMenu)
	ui.mainMenu.AddItem("Show Examples", "Display usage examples and command-line options", 'e', ui.showExamples)
	ui.mainMenu.AddItem("Exit", "Exit the Bella application", 'q', ui.confirmExit)

	// Configure status bar
	ui.statusBar.SetBorder(true).SetTitle(" Status ")
	ui.statusBar.SetText("[green]Ready[white] - Use Tab to navigate, Enter to select, Ctrl+C to exit")

	// Create main layout
	flex := tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(ui.mainMenu, 0, 1, true).
		AddItem(ui.statusBar, 3, 0, false)

	// Add main page
	ui.pages.AddPage("main", flex, true, true)

	// Set root and configure app
	ui.app.SetRoot(ui.pages, true).SetFocus(ui.mainMenu)

	// Handle global key events
	ui.app.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		switch event.Key() {
		case tcell.KeyCtrlC:
			ui.app.Stop()
			return nil
		case tcell.KeyEscape:
			// Go back to main menu if not already there
			frontPage, _ := ui.pages.GetFrontPage()
			if frontPage != "main" {
				ui.showMainMenu()
			}
			return nil
		}
		return event
	})
}

// Run starts the tview application
func (ui *TViewUI) Run() error {
	return ui.app.Run()
}

// showMainMenu displays the main menu
func (ui *TViewUI) showMainMenu() {
	ui.pages.SwitchToPage("main")
	ui.app.SetFocus(ui.mainMenu)
	ui.setStatus("[green]Ready[white] - Use Tab to navigate, Enter to select, Ctrl+C to exit")
}

// setStatus updates the status bar
func (ui *TViewUI) setStatus(message string) {
	ui.statusBar.SetText(message)
}

// showCopyMenu displays the copy configuration menu
func (ui *TViewUI) showCopyMenu() {
	ui.setStatus("[yellow]Copy Menu[white] - Configure copy operation - Use Tab to navigate")

	form := tview.NewForm()
	form.SetBorder(true).SetTitle(" Copy Configuration ")

	// Add form fields with proper defaults
	form.AddInputField("Source", "", 50, nil, nil)
	form.AddInputField("Destination", "", 50, nil, nil)
	form.AddInputField("Block Size", "4096", 20, nil, nil)
	form.AddInputField("Count", "-1", 20, nil, nil)
	form.AddInputField("Skip", "0", 20, nil, nil)
	form.AddInputField("Seek", "0", 20, nil, nil)
	form.AddInputField("Threads", "1", 10, nil, nil)
	form.AddInputField("Throttle (bytes/sec)", "0", 20, nil, nil)
	form.AddInputField("Temp Directory", "", 30, nil, nil)

	// Checkboxes for boolean options
	form.AddCheckbox("Progress", true, nil)
	form.AddCheckbox("Verify", false, nil)
	form.AddCheckbox("Sparse", false, nil)
	form.AddCheckbox("Sparse Only", false, nil)
	form.AddCheckbox("Skip Empty", false, nil)
	form.AddCheckbox("Partition Only", false, nil)
	form.AddCheckbox("Append Mode", false, nil)
	form.AddCheckbox("Dry Run", false, nil)
	form.AddCheckbox("Skip Bad Sectors", false, nil)
	form.AddCheckbox("JSON Memory", false, nil)
	form.AddCheckbox("Checkpoint Memory", false, nil)

	// Dropdowns for selections
	form.AddDropDown("Compression", []string{"none", "gzip1", "gzip6", "gzip9"}, 0, nil)
	form.AddDropDown("Decompression", []string{"auto", "gzip", "none"}, 0, nil)
	form.AddDropDown("Checksum", []string{"none", "md5", "sha256", "sha512"}, 0, nil)

	// Add buttons
	form.AddButton("Start Copy", func() {
		ui.startCopyOperation(form)
	})
	form.AddButton("Browse Source", func() {
		ui.showDeviceBrowser("source", form)
	})
	form.AddButton("Browse Destination", func() {
		ui.showDeviceBrowser("destination", form)
	})
	form.AddButton("Back", ui.showMainMenu)

	// Create layout
	flex := tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(form, 0, 1, true)

	ui.pages.AddPage("copy-menu", flex, true, false)
	ui.pages.SwitchToPage("copy-menu")
	ui.app.SetFocus(form)
}

// showWipeMenu displays the wipe configuration menu
func (ui *TViewUI) showWipeMenu() {
	ui.setStatus("[yellow]Wipe Menu[white] - Configure wipe operation - Use Tab to navigate")

	form := tview.NewForm()
	form.SetBorder(true).SetTitle(" Wipe Configuration ")

	// Add form fields
	form.AddInputField("Target", "", 50, nil, nil)
	form.AddDropDown("Mode", []string{"zero", "random"}, 0, nil)
	form.AddInputField("Count", "-1", 20, nil, nil)
	form.AddCheckbox("Progress", true, nil)
	form.AddCheckbox("Dry Run", false, nil)

	// Add buttons
	form.AddButton("Start Wipe", func() {
		ui.startWipeOperation(form)
	})
	form.AddButton("Browse Target", func() {
		ui.showDeviceBrowser("target", form)
	})
	form.AddButton("Back", ui.showMainMenu)

	// Create layout
	flex := tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(form, 0, 1, true)

	ui.pages.AddPage("wipe-menu", flex, true, false)
	ui.pages.SwitchToPage("wipe-menu")
	ui.app.SetFocus(form)
}

// showVerifyMenu displays the verify configuration menu
func (ui *TViewUI) showVerifyMenu() {
	ui.setStatus("[yellow]Verify Menu[white] - Configure verification operation - Use Tab to navigate")

	form := tview.NewForm()
	form.SetBorder(true).SetTitle(" Verify Configuration ")

	// Add form fields
	form.AddInputField("Source", "", 50, nil, nil)
	form.AddInputField("Target", "", 50, nil, nil)
	form.AddInputField("Block Size", "4096", 20, nil, nil)
	form.AddDropDown("Checksum", []string{"none", "md5", "sha256", "sha512"}, 0, nil)
	form.AddCheckbox("Progress", true, nil)

	// Add buttons
	form.AddButton("Start Verify", func() {
		ui.startVerifyOperation(form)
	})
	form.AddButton("Browse Source", func() {
		ui.showDeviceBrowser("source", form)
	})
	form.AddButton("Browse Target", func() {
		ui.showDeviceBrowser("target", form)
	})
	form.AddButton("Back", ui.showMainMenu)

	// Create layout
	flex := tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(form, 0, 1, true)

	ui.pages.AddPage("verify-menu", flex, true, false)
	ui.pages.SwitchToPage("verify-menu")
	ui.app.SetFocus(form)
}

// showDeviceInfoMenu displays the device info menu
func (ui *TViewUI) showDeviceInfoMenu() {
	ui.setStatus("[yellow]Device Info[white] - Select device for information - Use Tab to navigate")

	list := tview.NewList()
	list.SetBorder(true).SetTitle(" Select Device ")

	// Detect real devices
	devices, err := detectDevices()
	if err != nil {
		ui.showError(fmt.Sprintf("Device Detection Error: Failed to detect devices: %v", err))
		return
	}

	if len(devices) == 0 {
		list.AddItem("No devices found", "No storage devices detected", 0, nil)
	} else {
		for _, device := range devices {
			devicePath := device.Path
			description := fmt.Sprintf("%s - %s", humanizeBytes(uint64(device.Size)), device.Model)
			if device.Filesystem != "" {
				description += fmt.Sprintf(" [%s]", device.Filesystem)
			}
			list.AddItem(device.Name, description, 0, func() {
				ui.showDeviceInfo(devicePath)
			})
		}
	}

	list.AddItem("Back", "Return to main menu", 'b', ui.showMainMenu)

	// Create layout
	flex := tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(list, 0, 1, true)

	ui.pages.AddPage("device-info-menu", flex, true, false)
	ui.pages.SwitchToPage("device-info-menu")
	ui.app.SetFocus(list)
}

// showExamples displays usage examples
func (ui *TViewUI) showExamples() {
	ui.setStatus("[yellow]Examples[white] - Usage examples and help")

	textView := tview.NewTextView()
	textView.SetBorder(true).SetTitle(" Examples and Usage ")

	// Get examples text (reuse existing function)
	examples := getExamplesText()
	textView.SetText(examples)

	// Create a flex layout with back button
	backButton := tview.NewButton("Back to Main Menu").SetSelectedFunc(ui.showMainMenu)

	flex := tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(textView, 0, 1, true).
		AddItem(backButton, 1, 0, false)

	ui.pages.AddPage("examples", flex, true, false)
	ui.pages.SwitchToPage("examples")
	ui.app.SetFocus(textView)
}

// confirmExit shows exit confirmation dialog
func (ui *TViewUI) confirmExit() {
	modal := tview.NewModal().
		SetText("Are you sure you want to exit Bella?").
		AddButtons([]string{"Yes", "No"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			if buttonLabel == "Yes" {
				ui.app.Stop()
			} else {
				ui.pages.RemovePage("exit-confirm")
				ui.showMainMenu()
			}
		})

	ui.pages.AddPage("exit-confirm", modal, false, false)
	ui.pages.SwitchToPage("exit-confirm")
	ui.app.SetFocus(modal)
}

// showNotImplemented shows a placeholder for not yet implemented features
func (ui *TViewUI) showNotImplemented(feature string) {
	modal := tview.NewModal().
		SetText(fmt.Sprintf("%s is not yet implemented in the new UI.\nThis will be added in the next update.", feature)).
		AddButtons([]string{"OK"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			ui.pages.RemovePage("not-implemented")
			ui.showMainMenu()
		})

	ui.pages.AddPage("not-implemented", modal, false, false)
	ui.pages.SwitchToPage("not-implemented")
	ui.app.SetFocus(modal)
}

// getExamplesText returns the examples text (reuse existing logic)
func getExamplesText() string {
	return `[yellow]Bella - Advanced Data Copier Examples[white]

[green]Basic Copy Operations:[white]
  bella copy /dev/sda /dev/sdb
    Copy entire disk sda to sdb

  bella copy file.img /dev/sdc
    Write image file to device sdc

  bella copy /dev/sda backup.img
    Create backup image of device sda

[green]Advanced Copy Options:[white]
  bella copy -bs=1M -progress /dev/sda /dev/sdb
    Copy with 1MB block size and progress display

  bella copy -compress=6 large_file.img compressed.img.gz
    Copy with gzip compression level 6

  bella copy -verify -checksum=sha256 source.img dest.img
    Copy with verification and SHA256 checksum

[green]Sparse File Support:[white]
  bella copy -sparse source.img dest.img
    Copy preserving sparse regions (holes)

[green]Wipe Operations:[white]
  bella wipe -mode=zero /dev/sdc
    Securely wipe device with zeros

  bella wipe -mode=random -passes=3 /dev/sdc
    Wipe with random data, 3 passes

[green]Verification:[white]
  bella verify source.img dest.img
    Compare two files for integrity

[green]Device Information:[white]
  bella info /dev/sda
    Show detailed device information

[green]Interactive Mode:[white]
  bella
    Start interactive menu (current mode)

[cyan]Press Escape or use Back button to return to main menu[white]`
}

// startCopyOperation starts the copy operation with the form data
func (ui *TViewUI) startCopyOperation(form *tview.Form) {
	// Extract all form values by index
	source := form.GetFormItem(0).(*tview.InputField).GetText()
	destination := form.GetFormItem(1).(*tview.InputField).GetText()
	blockSizeStr := form.GetFormItem(2).(*tview.InputField).GetText()
	countStr := form.GetFormItem(3).(*tview.InputField).GetText()
	skipStr := form.GetFormItem(4).(*tview.InputField).GetText()
	seekStr := form.GetFormItem(5).(*tview.InputField).GetText()
	_ = form.GetFormItem(6).(*tview.InputField).GetText() // threadsStr - not used yet
	_ = form.GetFormItem(7).(*tview.InputField).GetText() // throttleStr - not used yet
	tempDirStr := form.GetFormItem(8).(*tview.InputField).GetText()

	// Get checkbox values
	progress := form.GetFormItem(9).(*tview.Checkbox).IsChecked()
	verify := form.GetFormItem(10).(*tview.Checkbox).IsChecked()
	sparse := form.GetFormItem(11).(*tview.Checkbox).IsChecked()
	sparseOnly := form.GetFormItem(12).(*tview.Checkbox).IsChecked()
	skipEmpty := form.GetFormItem(13).(*tview.Checkbox).IsChecked()
	partitionOnly := form.GetFormItem(14).(*tview.Checkbox).IsChecked()
	appendMode := form.GetFormItem(15).(*tview.Checkbox).IsChecked()
	dryRun := form.GetFormItem(16).(*tview.Checkbox).IsChecked()
	skipBadSectors := form.GetFormItem(17).(*tview.Checkbox).IsChecked()
	jsonMemory := form.GetFormItem(18).(*tview.Checkbox).IsChecked()
	checkpointMem := form.GetFormItem(19).(*tview.Checkbox).IsChecked()

	// Get dropdown values
	_, compressionOption := form.GetFormItem(20).(*tview.DropDown).GetCurrentOption()
	_, decompressionOption := form.GetFormItem(21).(*tview.DropDown).GetCurrentOption()
	_, checksumOption := form.GetFormItem(22).(*tview.DropDown).GetCurrentOption()

	// Validate required fields
	if source == "" || destination == "" {
		ui.showError("Please specify both source and destination")
		return
	}

	// Parse numeric values
	blockSize, err := parseBlockSize(blockSizeStr)
	if err != nil {
		ui.showError(fmt.Sprintf("Invalid block size: %v", err))
		return
	}

	count, err := strconv.Atoi(countStr)
	if err != nil {
		ui.showError(fmt.Sprintf("Invalid count: %v", err))
		return
	}

	skip, err := strconv.ParseInt(skipStr, 10, 64)
	if err != nil {
		ui.showError(fmt.Sprintf("Invalid skip value: %v", err))
		return
	}

	seek, err := strconv.ParseInt(seekStr, 10, 64)
	if err != nil {
		ui.showError(fmt.Sprintf("Invalid seek value: %v", err))
		return
	}

	// Show confirmation dialog
	summary := fmt.Sprintf(`Copy Operation Summary:

Source: %s
Destination: %s
Block Size: %d bytes
Count: %d blocks
Skip: %d blocks
Seek: %d blocks
Progress: %v
Verify: %v
Sparse: %v
Partition Only: %v
Dry Run: %v

Proceed with copy operation?`,
		source, destination, blockSize, count, skip, seek,
		progress, verify, sparse, partitionOnly, dryRun)

	modal := tview.NewModal().
		SetText(summary).
		AddButtons([]string{"Yes", "No"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			ui.pages.RemovePage("copy-confirm")
			if buttonLabel == "Yes" {
				ui.executeCopyOperation(source, destination, blockSize, count, skip, seek,
					compressionOption, decompressionOption, progress, verify, checksumOption,
					sparse, sparseOnly, skipEmpty, partitionOnly, appendMode, dryRun,
					skipBadSectors, jsonMemory, checkpointMem, tempDirStr)
			} else {
				ui.pages.SwitchToPage("copy-menu")
			}
		})

	ui.pages.AddPage("copy-confirm", modal, false, false)
	ui.pages.SwitchToPage("copy-confirm")
	ui.app.SetFocus(modal)
}

// parseBlockSize parses a block size string (e.g., "4K", "1M", "512")
func parseBlockSize(sizeStr string) (int, error) {
	if sizeStr == "" {
		return 4096, nil // Default
	}

	// Handle numeric-only values
	if val, err := strconv.Atoi(sizeStr); err == nil {
		return val, nil
	}

	// Handle suffixed values
	sizeStr = strings.ToUpper(strings.TrimSpace(sizeStr))

	var multiplier int64 = 1
	var numStr string

	if strings.HasSuffix(sizeStr, "K") {
		multiplier = 1024
		numStr = strings.TrimSuffix(sizeStr, "K")
	} else if strings.HasSuffix(sizeStr, "M") {
		multiplier = 1024 * 1024
		numStr = strings.TrimSuffix(sizeStr, "M")
	} else if strings.HasSuffix(sizeStr, "G") {
		multiplier = 1024 * 1024 * 1024
		numStr = strings.TrimSuffix(sizeStr, "G")
	} else {
		numStr = sizeStr
	}

	val, err := strconv.ParseInt(numStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("invalid block size format: %s", sizeStr)
	}

	result := val * multiplier
	if result > int64(^uint(0)>>1) { // Check for int overflow
		return 0, fmt.Errorf("block size too large: %s", sizeStr)
	}

	return int(result), nil
}

// executeCopyOperation executes the actual copy operation
func (ui *TViewUI) executeCopyOperation(source, destination string, blockSize, count int, skip, seek int64,
	compressionOption, decompressionOption string, progress, verify bool, checksumOption string,
	sparse, sparseOnly, skipEmpty, partitionOnly, appendMode, dryRun, skipBadSectors, jsonMemory, checkpointMem bool, tempDirStr string) {

	// Show progress dialog
	ui.setStatus("[yellow]Copy Operation[white] - Running...")

	// Execute the copy operation using the existing function
	err := doCopyWithError(source, destination, blockSize, count, skip, seek,
		compressionOption, decompressionOption, progress, verify, checksumOption)

	if err != nil {
		ui.showError(fmt.Sprintf("Copy operation failed: %v", err))
	} else {
		modal := tview.NewModal().
			SetText("Copy operation completed successfully!").
			AddButtons([]string{"OK"}).
			SetDoneFunc(func(buttonIndex int, buttonLabel string) {
				ui.pages.RemovePage("copy-success")
				ui.showMainMenu()
			})

		ui.pages.AddPage("copy-success", modal, false, false)
		ui.pages.SwitchToPage("copy-success")
		ui.app.SetFocus(modal)
	}
}

// startWipeOperation starts the wipe operation with the form data
func (ui *TViewUI) startWipeOperation(form *tview.Form) {
	// Get form values by index
	target := form.GetFormItem(0).(*tview.InputField).GetText()
	_, mode := form.GetFormItem(1).(*tview.DropDown).GetCurrentOption()
	blockSizeStr := form.GetFormItem(2).(*tview.InputField).GetText()
	countStr := form.GetFormItem(3).(*tview.InputField).GetText()
	progress := form.GetFormItem(4).(*tview.Checkbox).IsChecked()
	dryRun := form.GetFormItem(5).(*tview.Checkbox).IsChecked()

	// Validate inputs
	if target == "" {
		ui.showError("Target is required")
		return
	}

	// Parse values
	blockSize, err := parseBlockSize(blockSizeStr)
	if err != nil {
		ui.showError(fmt.Sprintf("Invalid block size: %v", err))
		return
	}

	count, err := strconv.Atoi(countStr)
	if err != nil {
		ui.showError(fmt.Sprintf("Invalid count: %v", err))
		return
	}

	// Show confirmation dialog
	modal := tview.NewModal().
		SetText(fmt.Sprintf("Start wipe operation?\n\nTarget: %s\nMode: %s\nBlock Size: %d\nCount: %d\nDry Run: %v\n\n[red]WARNING: This will permanently destroy data![white]",
			target, mode, blockSize, count, dryRun)).
		AddButtons([]string{"Yes", "No"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			ui.pages.RemovePage("wipe-confirm")
			if buttonLabel == "Yes" {
				ui.executeWipeOperation(target, mode, blockSize, count, progress, dryRun)
			} else {
				ui.pages.SwitchToPage("wipe-menu")
			}
		})

	ui.pages.AddPage("wipe-confirm", modal, false, false)
	ui.pages.SwitchToPage("wipe-confirm")
	ui.app.SetFocus(modal)
}

// executeWipeOperation executes the actual wipe operation
func (ui *TViewUI) executeWipeOperation(target, mode string, blockSize, count int, progress, dryRun bool) {
	ui.setStatus("[yellow]Wipe Operation[white] - Running...")

	// Execute the wipe operation using the existing function
	err := doWipeWithError(target, mode, blockSize, count, progress, dryRun)

	if err != nil {
		ui.showError(fmt.Sprintf("Wipe operation failed: %v", err))
	} else {
		modal := tview.NewModal().
			SetText("Wipe operation completed successfully!").
			AddButtons([]string{"OK"}).
			SetDoneFunc(func(buttonIndex int, buttonLabel string) {
				ui.pages.RemovePage("wipe-success")
				ui.showMainMenu()
			})

		ui.pages.AddPage("wipe-success", modal, false, false)
		ui.pages.SwitchToPage("wipe-success")
		ui.app.SetFocus(modal)
	}
}

// startVerifyOperation starts the verify operation with the form data
func (ui *TViewUI) startVerifyOperation(form *tview.Form) {
	// Get form values by index
	source := form.GetFormItem(0).(*tview.InputField).GetText()
	target := form.GetFormItem(1).(*tview.InputField).GetText()
	blockSizeStr := form.GetFormItem(2).(*tview.InputField).GetText()
	_, checksumOption := form.GetFormItem(3).(*tview.DropDown).GetCurrentOption()
	progress := form.GetFormItem(4).(*tview.Checkbox).IsChecked()

	// Validate inputs
	if source == "" || target == "" {
		ui.showError("Source and target are required")
		return
	}

	// Parse block size
	blockSize, err := parseBlockSize(blockSizeStr)
	if err != nil {
		ui.showError(fmt.Sprintf("Invalid block size: %v", err))
		return
	}

	// Show confirmation dialog
	modal := tview.NewModal().
		SetText(fmt.Sprintf("Start verification?\n\nSource: %s\nTarget: %s\nBlock Size: %d\nChecksum: %s\nProgress: %v",
			source, target, blockSize, checksumOption, progress)).
		AddButtons([]string{"Yes", "No"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			ui.pages.RemovePage("verify-confirm")
			if buttonLabel == "Yes" {
				ui.executeVerifyOperation(source, target, blockSize, checksumOption, progress)
			} else {
				ui.pages.SwitchToPage("verify-menu")
			}
		})

	ui.pages.AddPage("verify-confirm", modal, false, false)
	ui.pages.SwitchToPage("verify-confirm")
	ui.app.SetFocus(modal)
}

// executeVerifyOperation executes the actual verify operation
func (ui *TViewUI) executeVerifyOperation(source, target string, blockSize int, checksumOption string, progress bool) {
	ui.setStatus("[yellow]Verify Operation[white] - Running...")

	// Execute the verify operation using the existing function
	match := compareFiles(source, target, blockSize)

	var message string
	if match {
		message = "Verification completed successfully!\n\nFiles match perfectly."
	} else {
		message = "Verification failed!\n\nFiles do not match."
	}

	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"OK"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			ui.pages.RemovePage("verify-result")
			ui.showMainMenu()
		})

	ui.pages.AddPage("verify-result", modal, false, false)
	ui.pages.SwitchToPage("verify-result")
	ui.app.SetFocus(modal)
}

// showDeviceInfo displays information for a specific device
func (ui *TViewUI) showDeviceInfo(devicePath string) {
	ui.setStatus(fmt.Sprintf("[yellow]Device Info[white] - %s", devicePath))

	textView := tview.NewTextView()
	textView.SetBorder(true).SetTitle(fmt.Sprintf(" Device Information: %s ", devicePath))

	// Get real device information
	devices, err := detectDevices()
	var info string

	if err != nil {
		info = fmt.Sprintf(`Device: %s

[red]Error:[white] Failed to detect device information: %v

Press Escape or Back button to return to device menu.`, devicePath, err)
	} else {
		// Find the device in our detected devices
		var deviceInfo *DeviceInfo
		for _, device := range devices {
			if device.Path == devicePath {
				deviceInfo = &device
				break
			}
		}

		if deviceInfo == nil {
			info = fmt.Sprintf(`Device: %s

[red]Error:[white] Device not found or not accessible

Press Escape or Back button to return to device menu.`, devicePath)
		} else {
			info = fmt.Sprintf(`Device: %s

[yellow]Basic Information:[white]
  Name: %s
  Path: %s
  Model: %s
  Vendor: %s

[yellow]Size Information:[white]
  Total Size: %s
  Block Size: %d bytes

[yellow]Properties:[white]
  Removable: %v
  Read-only: %v
  Rotational: %v

[yellow]Filesystem:[white]
  Type: %s
  Mount Point: %s

Press Escape or Back button to return to device menu.`,
				devicePath,
				deviceInfo.Name,
				deviceInfo.Path,
				deviceInfo.Model,
				deviceInfo.Vendor,
				humanizeBytes(uint64(deviceInfo.Size)),
				deviceInfo.BlockSize,
				deviceInfo.Removable,
				deviceInfo.ReadOnly,
				deviceInfo.Rotational,
				deviceInfo.Filesystem,
				deviceInfo.Mountpoint)
		}
	}

	textView.SetText(info)

	// Create a flex layout with back button
	backButton := tview.NewButton("Back to Device Menu").SetSelectedFunc(ui.showDeviceInfoMenu)

	flex := tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(textView, 0, 1, true).
		AddItem(backButton, 1, 0, false)

	ui.pages.AddPage("device-info", flex, true, false)
	ui.pages.SwitchToPage("device-info")
	ui.app.SetFocus(textView)
}

// showDeviceBrowser shows a device and file browser
func (ui *TViewUI) showDeviceBrowser(target string, form *tview.Form) {
	ui.setStatus(fmt.Sprintf("[yellow]Device Browser[white] - Select %s - Use Tab to navigate", target))

	list := tview.NewList()
	list.SetBorder(true).SetTitle(fmt.Sprintf(" Select %s ", target))

	// Add manual entry option
	list.AddItem("Manual Entry", "Type path manually", 0, func() {
		ui.showManualEntry(target, form)
	})

	// Detect and add real devices
	devices, err := detectDevices()
	if err == nil && len(devices) > 0 {
		list.AddItem("--- Detected Devices ---", "", 0, nil)
		for _, device := range devices {
			devicePath := device.Path
			description := fmt.Sprintf("%s - %s", humanizeBytes(uint64(device.Size)), device.Model)
			if device.Filesystem != "" {
				description += fmt.Sprintf(" [%s]", device.Filesystem)
			}
			list.AddItem(device.Path, description, 0, func() {
				ui.setFormField(form, target, devicePath)
				ui.showMainMenu() // Return to the form
			})
		}
	}

	// Add common file paths
	list.AddItem("--- Common Paths ---", "", 0, nil)
	commonPaths := []string{
		"/home",
		"/tmp",
		"/var",
		"/opt",
	}

	for _, path := range commonPaths {
		pathCopy := path
		list.AddItem(path, fmt.Sprintf("Browse %s directory", path), 0, func() {
			ui.setFormField(form, target, pathCopy)
			ui.showMainMenu() // Return to the form
		})
	}

	list.AddItem("Back", "Return to form", 0, func() {
		ui.showMainMenu() // Return to the form
	})

	ui.pages.AddPage("device-browser", list, true, false)
	ui.pages.SwitchToPage("device-browser")
	ui.app.SetFocus(list)
}

// showManualEntry shows a manual path entry dialog
func (ui *TViewUI) showManualEntry(target string, form *tview.Form) {
	modal := tview.NewModal()
	modal.SetText(fmt.Sprintf("Enter path for %s:", target))
	modal.AddButtons([]string{"OK", "Cancel"})
	modal.SetDoneFunc(func(buttonIndex int, buttonLabel string) {
		if buttonLabel == "OK" {
			// Get the input text (this is a simplified version)
			// In a real implementation, you'd use an InputField in the modal
			ui.setFormField(form, target, "/dev/sda") // Placeholder
		}
		ui.showMainMenu()
	})

	ui.pages.AddPage("manual-entry", modal, true, false)
	ui.pages.SwitchToPage("manual-entry")
}

// setFormField sets a field value in the form
func (ui *TViewUI) setFormField(form *tview.Form, fieldName, value string) {
	// Find and set the field value
	for i := 0; i < form.GetFormItemCount(); i++ {
		if field, ok := form.GetFormItem(i).(*tview.InputField); ok {
			if field.GetLabel() == fieldName ||
				(fieldName == "source" && field.GetLabel() == "Source") ||
				(fieldName == "destination" && field.GetLabel() == "Destination") ||
				(fieldName == "target" && field.GetLabel() == "Target") {
				field.SetText(value)
				break
			}
		}
	}
}

// showFileBrowser shows a simple file browser (placeholder)
func (ui *TViewUI) showFileBrowser(target string, form *tview.Form) {
	ui.showDeviceBrowser(target, form)
}

// showError displays an error message
func (ui *TViewUI) showError(message string) {
	modal := tview.NewModal().
		SetText(fmt.Sprintf("Error: %s", message)).
		AddButtons([]string{"OK"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			ui.pages.RemovePage("error")
		})

	ui.pages.AddPage("error", modal, false, false)
	ui.pages.SwitchToPage("error")
	ui.app.SetFocus(modal)
}

// runNewInteractiveMenu starts the new tview-based interactive menu
func runNewInteractiveMenu() error {
	ui := NewTViewUI()
	return ui.Run()
}
